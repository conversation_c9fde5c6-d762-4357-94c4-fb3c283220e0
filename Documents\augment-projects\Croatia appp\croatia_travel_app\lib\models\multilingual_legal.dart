// 🌍 MULTILINGUAL LEGAL MODELS - Multi-language legal documents

class LegalDocument {
  final String id;
  final String type;
  final String title;
  final String content;
  final String language;
  final String version;
  final DateTime effectiveDate;
  final DateTime lastUpdated;
  final String? translatedFrom;
  final String? translatorId;
  final ReviewStatus reviewStatus;
  final Map<String, dynamic> metadata;
  final String? reviewerId;
  final String? reviewNotes;
  final DateTime? reviewedAt;
  final List<String>? corrections;

  const LegalDocument({
    required this.id,
    required this.type,
    required this.title,
    required this.content,
    required this.language,
    required this.version,
    required this.effectiveDate,
    required this.lastUpdated,
    this.translatedFrom,
    this.translatorId,
    required this.reviewStatus,
    required this.metadata,
    this.reviewerId,
    this.reviewNotes,
    this.reviewedAt,
    this.corrections,
  });

  factory LegalDocument.fromJson(Map<String, dynamic> json) {
    return LegalDocument(
      id: json['id'] as String,
      type: json['type'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      language: json['language'] as String,
      version: json['version'] as String,
      effectiveDate: DateTime.parse(json['effectiveDate'] as String),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      translatedFrom: json['translatedFrom'] as String?,
      translatorId: json['translatorId'] as String?,
      reviewStatus: ReviewStatus.values.byName(json['reviewStatus'] as String),
      metadata: json['metadata'] as Map<String, dynamic>,
      reviewerId: json['reviewerId'] as String?,
      reviewNotes: json['reviewNotes'] as String?,
      reviewedAt: json['reviewedAt'] != null
          ? DateTime.parse(json['reviewedAt'] as String)
          : null,
      corrections: (json['corrections'] as List<dynamic>?)?.cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'title': title,
      'content': content,
      'language': language,
      'version': version,
      'effectiveDate': effectiveDate.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'translatedFrom': translatedFrom,
      'translatorId': translatorId,
      'reviewStatus': reviewStatus.name,
      'metadata': metadata,
      'reviewerId': reviewerId,
      'reviewNotes': reviewNotes,
      'reviewedAt': reviewedAt?.toIso8601String(),
      'corrections': corrections,
    };
  }

  LegalDocument copyWith({
    String? id,
    String? type,
    String? title,
    String? content,
    String? language,
    String? version,
    DateTime? effectiveDate,
    DateTime? lastUpdated,
    String? translatedFrom,
    String? translatorId,
    ReviewStatus? reviewStatus,
    Map<String, dynamic>? metadata,
    String? reviewerId,
    String? reviewNotes,
    DateTime? reviewedAt,
    List<String>? corrections,
  }) {
    return LegalDocument(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      content: content ?? this.content,
      language: language ?? this.language,
      version: version ?? this.version,
      effectiveDate: effectiveDate ?? this.effectiveDate,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      translatedFrom: translatedFrom ?? this.translatedFrom,
      translatorId: translatorId ?? this.translatorId,
      reviewStatus: reviewStatus ?? this.reviewStatus,
      metadata: metadata ?? this.metadata,
      reviewerId: reviewerId ?? this.reviewerId,
      reviewNotes: reviewNotes ?? this.reviewNotes,
      reviewedAt: reviewedAt ?? this.reviewedAt,
      corrections: corrections ?? this.corrections,
    );
  }
}

class SupportedLanguage {
  final String code;
  final String name;
  final String nativeName;
  final bool isRTL;
  final bool isSupported;
  final double completionRate;

  const SupportedLanguage({
    required this.code,
    required this.name,
    required this.nativeName,
    this.isRTL = false,
    this.isSupported = true,
    this.completionRate = 0.0,
  });

  factory SupportedLanguage.fromJson(Map<String, dynamic> json) {
    return SupportedLanguage(
      code: json['code'] as String,
      name: json['name'] as String,
      nativeName: json['nativeName'] as String,
      isRTL: json['isRTL'] as bool? ?? false,
      isSupported: json['isSupported'] as bool? ?? true,
      completionRate: (json['completionRate'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'nativeName': nativeName,
      'isRTL': isRTL,
      'isSupported': isSupported,
      'completionRate': completionRate,
    };
  }
}

enum ReviewStatus { pending, approved, rejected, needsRevision }

enum TranslationStatus {
  notStarted,
  inProgress,
  translated,
  needsReview,
  approved,
  rejected,
}

enum TranslationEventType {
  translationCreated,
  translationApproved,
  translationRejected,
  bulkTranslationCompleted,
}

class TranslationEvent {
  final TranslationEventType type;
  final String documentType;
  final String? sourceLanguage;
  final String? targetLanguage;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  const TranslationEvent({
    required this.type,
    required this.documentType,
    this.sourceLanguage,
    this.targetLanguage,
    required this.timestamp,
    required this.data,
  });

  factory TranslationEvent.fromJson(Map<String, dynamic> json) {
    return TranslationEvent(
      type: TranslationEventType.values.byName(json['type'] as String),
      documentType: json['documentType'] as String,
      sourceLanguage: json['sourceLanguage'] as String?,
      targetLanguage: json['targetLanguage'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      data: json['data'] as Map<String, dynamic>,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'documentType': documentType,
      'sourceLanguage': sourceLanguage,
      'targetLanguage': targetLanguage,
      'timestamp': timestamp.toIso8601String(),
      'data': data,
    };
  }
}

class TranslationProgressReport {
  final int totalTranslations;
  final int completedTranslations;
  final int pendingTranslations;
  final int needsReviewTranslations;
  final double completionRate;
  final Map<String, double> languageProgress;
  final Map<String, double> documentProgress;
  final DateTime estimatedCompletion;

  const TranslationProgressReport({
    required this.totalTranslations,
    required this.completedTranslations,
    required this.pendingTranslations,
    required this.needsReviewTranslations,
    required this.completionRate,
    required this.languageProgress,
    required this.documentProgress,
    required this.estimatedCompletion,
  });

  factory TranslationProgressReport.fromJson(Map<String, dynamic> json) {
    return TranslationProgressReport(
      totalTranslations: json['totalTranslations'] as int,
      completedTranslations: json['completedTranslations'] as int,
      pendingTranslations: json['pendingTranslations'] as int,
      needsReviewTranslations: json['needsReviewTranslations'] as int,
      completionRate: (json['completionRate'] as num).toDouble(),
      languageProgress: Map<String, double>.from(
        json['languageProgress'] as Map,
      ),
      documentProgress: Map<String, double>.from(
        json['documentProgress'] as Map,
      ),
      estimatedCompletion: DateTime.parse(
        json['estimatedCompletion'] as String,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalTranslations': totalTranslations,
      'completedTranslations': completedTranslations,
      'pendingTranslations': pendingTranslations,
      'needsReviewTranslations': needsReviewTranslations,
      'completionRate': completionRate,
      'languageProgress': languageProgress,
      'documentProgress': documentProgress,
      'estimatedCompletion': estimatedCompletion.toIso8601String(),
    };
  }
}

enum InconsistencyType {
  versionMismatch,
  dateMismatch,
  missingTerms,
  structuralDifference,
}

enum InconsistencySeverity { low, medium, high, critical }

class LegalInconsistency {
  final String documentType;
  final String language;
  final InconsistencyType type;
  final String description;
  final InconsistencySeverity severity;

  const LegalInconsistency({
    required this.documentType,
    required this.language,
    required this.type,
    required this.description,
    required this.severity,
  });

  factory LegalInconsistency.fromJson(Map<String, dynamic> json) {
    return LegalInconsistency(
      documentType: json['documentType'] as String,
      language: json['language'] as String,
      type: InconsistencyType.values.byName(json['type'] as String),
      description: json['description'] as String,
      severity: InconsistencySeverity.values.byName(json['severity'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'documentType': documentType,
      'language': language,
      'type': type.name,
      'description': description,
      'severity': severity.name,
    };
  }
}

class ConsistencyReport {
  final DateTime checkedAt;
  final int totalDocuments;
  final List<LegalInconsistency> inconsistencies;
  final double overallScore;
  final List<String> recommendations;

  const ConsistencyReport({
    required this.checkedAt,
    required this.totalDocuments,
    required this.inconsistencies,
    required this.overallScore,
    required this.recommendations,
  });

  factory ConsistencyReport.fromJson(Map<String, dynamic> json) {
    return ConsistencyReport(
      checkedAt: DateTime.parse(json['checkedAt'] as String),
      totalDocuments: json['totalDocuments'] as int,
      inconsistencies: (json['inconsistencies'] as List<dynamic>)
          .map((e) => LegalInconsistency.fromJson(e as Map<String, dynamic>))
          .toList(),
      overallScore: (json['overallScore'] as num).toDouble(),
      recommendations: (json['recommendations'] as List<dynamic>)
          .cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'checkedAt': checkedAt.toIso8601String(),
      'totalDocuments': totalDocuments,
      'inconsistencies': inconsistencies.map((e) => e.toJson()).toList(),
      'overallScore': overallScore,
      'recommendations': recommendations,
    };
  }
}
