// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'penetration_testing.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SecurityTest _$SecurityTestFromJson(Map<String, dynamic> json) => SecurityTest(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: $enumDecode(_$TestCategoryEnumMap, json['category']),
      severity: $enumDecode(_$TestSeverityEnumMap, json['severity']),
      estimatedDuration:
          Duration(microseconds: (json['estimatedDuration'] as num).toInt()),
      requirements: (json['requirements'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$SecurityTestToJson(SecurityTest instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'category': _$TestCategoryEnumMap[instance.category]!,
      'severity': _$TestSeverityEnumMap[instance.severity]!,
      'estimatedDuration': instance.estimatedDuration.inMicroseconds,
      'requirements': instance.requirements,
    };

const _$TestCategoryEnumMap = {
  TestCategory.injection: 'injection',
  TestCategory.authentication: 'authentication',
  TestCategory.cryptography: 'cryptography',
  TestCategory.authorization: 'authorization',
  TestCategory.dataValidation: 'dataValidation',
  TestCategory.sessionManagement: 'sessionManagement',
  TestCategory.configuration: 'configuration',
};

const _$TestSeverityEnumMap = {
  TestSeverity.low: 'low',
  TestSeverity.medium: 'medium',
  TestSeverity.high: 'high',
  TestSeverity.critical: 'critical',
};

Vulnerability _$VulnerabilityFromJson(Map<String, dynamic> json) =>
    Vulnerability(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      severity: $enumDecode(_$VulnerabilitySeverityEnumMap, json['severity']),
      category: $enumDecode(_$VulnerabilityCategoryEnumMap, json['category']),
      cwe: json['cwe'] as String,
      owasp: json['owasp'] as String,
      affectedComponent: json['affectedComponent'] as String,
      testCase: json['testCase'] as String,
      recommendation: json['recommendation'] as String,
      references: (json['references'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$VulnerabilityToJson(Vulnerability instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'severity': _$VulnerabilitySeverityEnumMap[instance.severity]!,
      'category': _$VulnerabilityCategoryEnumMap[instance.category]!,
      'cwe': instance.cwe,
      'owasp': instance.owasp,
      'affectedComponent': instance.affectedComponent,
      'testCase': instance.testCase,
      'recommendation': instance.recommendation,
      'references': instance.references,
    };

const _$VulnerabilitySeverityEnumMap = {
  VulnerabilitySeverity.info: 'info',
  VulnerabilitySeverity.low: 'low',
  VulnerabilitySeverity.medium: 'medium',
  VulnerabilitySeverity.high: 'high',
  VulnerabilitySeverity.critical: 'critical',
};

const _$VulnerabilityCategoryEnumMap = {
  VulnerabilityCategory.injection: 'injection',
  VulnerabilityCategory.authentication: 'authentication',
  VulnerabilityCategory.cryptography: 'cryptography',
  VulnerabilityCategory.authorization: 'authorization',
  VulnerabilityCategory.dataExposure: 'dataExposure',
  VulnerabilityCategory.configuration: 'configuration',
  VulnerabilityCategory.businessLogic: 'businessLogic',
};

TestResult _$TestResultFromJson(Map<String, dynamic> json) => TestResult(
      testId: json['testId'] as String,
      testName: json['testName'] as String,
      passed: json['passed'] as bool,
      vulnerabilities: (json['vulnerabilities'] as List<dynamic>)
          .map((e) => Vulnerability.fromJson(e as Map<String, dynamic>))
          .toList(),
      executionTime:
          Duration(microseconds: (json['executionTime'] as num).toInt()),
      details: json['details'] as String,
    );

Map<String, dynamic> _$TestResultToJson(TestResult instance) =>
    <String, dynamic>{
      'testId': instance.testId,
      'testName': instance.testName,
      'passed': instance.passed,
      'vulnerabilities': instance.vulnerabilities,
      'executionTime': instance.executionTime.inMicroseconds,
      'details': instance.details,
    };

SecurityAuditReport _$SecurityAuditReportFromJson(Map<String, dynamic> json) =>
    SecurityAuditReport(
      id: json['id'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      duration: Duration(microseconds: (json['duration'] as num).toInt()),
      testResults: (json['testResults'] as List<dynamic>)
          .map((e) => TestResult.fromJson(e as Map<String, dynamic>))
          .toList(),
      criticalVulnerabilities: (json['criticalVulnerabilities'] as num).toInt(),
      highVulnerabilities: (json['highVulnerabilities'] as num).toInt(),
      mediumVulnerabilities: (json['mediumVulnerabilities'] as num).toInt(),
      lowVulnerabilities: (json['lowVulnerabilities'] as num).toInt(),
      securityScore: (json['securityScore'] as num).toDouble(),
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      complianceStatus: Map<String, bool>.from(json['complianceStatus'] as Map),
    );

Map<String, dynamic> _$SecurityAuditReportToJson(
        SecurityAuditReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime.toIso8601String(),
      'duration': instance.duration.inMicroseconds,
      'testResults': instance.testResults,
      'criticalVulnerabilities': instance.criticalVulnerabilities,
      'highVulnerabilities': instance.highVulnerabilities,
      'mediumVulnerabilities': instance.mediumVulnerabilities,
      'lowVulnerabilities': instance.lowVulnerabilities,
      'securityScore': instance.securityScore,
      'recommendations': instance.recommendations,
      'complianceStatus': instance.complianceStatus,
    };

SecurityEvent _$SecurityEventFromJson(Map<String, dynamic> json) =>
    SecurityEvent(
      type: $enumDecode(_$SecurityEventTypeEnumMap, json['type']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      data: json['data'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$SecurityEventToJson(SecurityEvent instance) =>
    <String, dynamic>{
      'type': _$SecurityEventTypeEnumMap[instance.type]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'data': instance.data,
    };

const _$SecurityEventTypeEnumMap = {
  SecurityEventType.testStarted: 'testStarted',
  SecurityEventType.testCompleted: 'testCompleted',
  SecurityEventType.vulnerabilityFound: 'vulnerabilityFound',
  SecurityEventType.auditCompleted: 'auditCompleted',
  SecurityEventType.scanInitiated: 'scanInitiated',
};

SecurityConfiguration _$SecurityConfigurationFromJson(
        Map<String, dynamic> json) =>
    SecurityConfiguration(
      enableAutomaticScanning: json['enableAutomaticScanning'] as bool,
      scanInterval:
          Duration(microseconds: (json['scanInterval'] as num).toInt()),
      enabledTestCategories: (json['enabledTestCategories'] as List<dynamic>)
          .map((e) => $enumDecode(_$TestCategoryEnumMap, e))
          .toList(),
      minimumSeverityLevel:
          $enumDecode(_$TestSeverityEnumMap, json['minimumSeverityLevel']),
      generateDetailedReports: json['generateDetailedReports'] as bool,
    );

Map<String, dynamic> _$SecurityConfigurationToJson(
        SecurityConfiguration instance) =>
    <String, dynamic>{
      'enableAutomaticScanning': instance.enableAutomaticScanning,
      'scanInterval': instance.scanInterval.inMicroseconds,
      'enabledTestCategories': instance.enabledTestCategories
          .map((e) => _$TestCategoryEnumMap[e]!)
          .toList(),
      'minimumSeverityLevel':
          _$TestSeverityEnumMap[instance.minimumSeverityLevel]!,
      'generateDetailedReports': instance.generateDetailedReports,
    };

SecurityMetrics _$SecurityMetricsFromJson(Map<String, dynamic> json) =>
    SecurityMetrics(
      reportDate: DateTime.parse(json['reportDate'] as String),
      totalTestsRun: (json['totalTestsRun'] as num).toInt(),
      vulnerabilitiesFound: (json['vulnerabilitiesFound'] as num).toInt(),
      vulnerabilitiesFixed: (json['vulnerabilitiesFixed'] as num).toInt(),
      averageSecurityScore: (json['averageSecurityScore'] as num).toDouble(),
      vulnerabilityBreakdown:
          (json['vulnerabilityBreakdown'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            $enumDecode(_$VulnerabilitySeverityEnumMap, k), (e as num).toInt()),
      ),
      topVulnerabilityTypes: (json['topVulnerabilityTypes'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$SecurityMetricsToJson(SecurityMetrics instance) =>
    <String, dynamic>{
      'reportDate': instance.reportDate.toIso8601String(),
      'totalTestsRun': instance.totalTestsRun,
      'vulnerabilitiesFound': instance.vulnerabilitiesFound,
      'vulnerabilitiesFixed': instance.vulnerabilitiesFixed,
      'averageSecurityScore': instance.averageSecurityScore,
      'vulnerabilityBreakdown': instance.vulnerabilityBreakdown
          .map((k, e) => MapEntry(_$VulnerabilitySeverityEnumMap[k]!, e)),
      'topVulnerabilityTypes': instance.topVulnerabilityTypes,
    };
