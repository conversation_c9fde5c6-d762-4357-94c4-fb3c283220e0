import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/ai_assistant.dart' as assistant;
import '../models/ai_orchestrator.dart' as orchestrator;
import 'ai_orchestrator_simple.dart';
import 'offline_ai_service.dart';

/// Služba pro AI asistenta
class AIAssistantService {
  static final AIAssistantService _instance = AIAssistantService._internal();
  factory AIAssistantService() => _instance;
  AIAssistantService._internal();

  // Hlasové služby
  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();

  // AI služby
  final AIOrchestrator _orchestrator = AIOrchestrator();
  final OfflineAIService _offlineAI = OfflineAIService();
  final Connectivity _connectivity = Connectivity();

  // Cache a stav
  final List<assistant.AIConversation> _conversations = [];
  assistant.AIConversation? _currentConversation;
  assistant.ConversationContext? _currentContext;
  bool _isOnline = true;
  bool _isListening = false;
  bool _isSpeaking = false;

  // Znalostní báze
  final Map<String, List<String>> _knowledgeBase = {
    'greeting': [
      'Ahoj! Jsem váš cestovní asistent pro Chorvatsko. Jak vám mohu pomoci?',
      'Dobrý den! Rád vám pomohu s plánováním vaší cesty po Chorvatsku.',
      'Zdravím! Jsem zde, abych vám poradil s cestováním po Chorvatsku.',
    ],
    'restaurants': [
      'Mohu vám doporučit skvělé restaurace v okolí. Jaký typ kuchyně preferujete?',
      'V Chorvatsku najdete vynikající mořské plody a tradiční dalmatskou kuchyni.',
      'Doporučuji vyzkoušet pašticadu, crni rižot nebo fresh fish z místních trhů.',
    ],
    'attractions': [
      'Chorvatsko má nádherné památky UNESCO - Dubrovník, Split, Plitvická jezera...',
      'Nejoblíbenější atrakce zahrnují historická centra, národní parky a ostrovy.',
      'Podle vaší polohy vám mohu doporučit nejbližší zajímavá místa.',
    ],
    'weather': [
      'Chorvatsko má středomořské klima s teplými léty a mírnými zimami.',
      'Nejlepší čas pro návštěvu je od května do září.',
      'V létě jsou teploty kolem 25-30°C, ideální pro plavání.',
    ],
    'emergency': [
      'V nouzové situaci volejte 112 (obecné nouzové číslo) nebo 192 (policie).',
      'Zdravotnická pomoc: 194, Hasiči: 193.',
      'Turistická policie má speciální linku pro cizince.',
    ],
  };

  /// Inicializace AI asistenta
  Future<void> initialize() async {
    try {
      // Inicializace orchestrátoru
      await _orchestrator.initialize();

      // Inicializace hlasových služb
      await _initializeSpeechServices();

      // Načtení uložených konverzací
      await _loadConversations();

      // Nastavení výchozího kontextu
      _currentContext = assistant.ConversationContext(
        userPreferences: [],
        sessionData: {},
      );

      debugPrint('AI Assistant inicializován úspěšně');
    } catch (e) {
      debugPrint('Chyba při inicializaci AI Assistant: $e');
    }
  }

  /// Inicializace hlasových služeb
  Future<void> _initializeSpeechServices() async {
    try {
      // Speech to Text
      bool available = await _speechToText.initialize(
        onStatus: (status) => debugPrint('Speech status: $status'),
        onError: (error) => debugPrint('Speech error: $error'),
      );

      if (!available) {
        debugPrint('Speech to Text není dostupný');
      }

      // Text to Speech
      await _flutterTts.setLanguage('cs-CZ');
      await _flutterTts.setSpeechRate(0.8);
      await _flutterTts.setVolume(0.8);
      await _flutterTts.setPitch(1.0);
    } catch (e) {
      debugPrint('Chyba při inicializaci hlasových služeb: $e');
    }
  }

  /// Načte uložené konverzace
  Future<void> _loadConversations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final conversationsJson = prefs.getString('ai_conversations');

      if (conversationsJson != null) {
        // TODO: Implementovat deserializaci konverzací
        debugPrint('Načítání uložených konverzací...');
      }
    } catch (e) {
      debugPrint('Chyba při načítání konverzací: $e');
    }
  }

  /// Zkontroluje připojení k internetu
  Future<void> _checkConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      _isOnline = !connectivityResults.contains(ConnectivityResult.none);
    } catch (e) {
      debugPrint('Chyba při kontrole připojení: $e');
      _isOnline = false;
    }
  }

  /// Zpracuje textovou zprávu od uživatele
  Future<assistant.AIMessage> processTextMessage(String message) async {
    try {
      // Vytvoří zprávu od uživatele
      final userMessage = assistant.AIMessage(
        id: _generateId(),
        conversationId: _getCurrentConversationId(),
        sender: assistant.MessageSender.user,
        content: message,
        type: assistant.MessageType.text,
        timestamp: DateTime.now(),
        isRead: true,
      );

      // Přidá do konverzace
      _addMessageToCurrentConversation(userMessage);

      // Generuje odpověď AI
      final aiResponse = await _generateAIResponse(message);

      // Vytvoří zprávu od AI
      final aiMessage = assistant.AIMessage(
        id: _generateId(),
        conversationId: _getCurrentConversationId(),
        sender: assistant.MessageSender.ai,
        content: aiResponse,
        type: assistant.MessageType.text,
        timestamp: DateTime.now(),
        isRead: false,
        metadata: assistant.AIMessageMetadata(
          confidence: 0.9,
          language: 'cs',
          source: assistant.AIResponseSource.knowledge,
        ),
      );

      // Přidá do konverzace
      _addMessageToCurrentConversation(aiMessage);

      return aiMessage;
    } catch (e) {
      debugPrint('Chyba při zpracování zprávy: $e');
      return _createErrorMessage();
    }
  }

  /// Generuje odpověď AI na základě zprávy
  Future<String> _generateAIResponse(String message) async {
    try {
      // Použití orchestrátoru pro inteligentní odpověď
      final response = await _orchestrator.processQuery(
        message,
        forceOffline: !_isOnline,
      );

      return response.content;
    } catch (e) {
      debugPrint('Chyba při generování odpovědi orchestrátorem: $e');
      return await _generateFallbackResponse(message);
    }
  }

  /// Fallback metoda pro generování odpovědi
  Future<String> _generateFallbackResponse(String message) async {
    // Zkontroluje připojení
    await _checkConnectivity();

    // Pokud je offline, použije offline AI
    if (!_isOnline) {
      final offlineResponse = await _offlineAI.findAnswer(message);
      if (offlineResponse != null) {
        return '$offlineResponse\n\n💡 *Odpověď z offline režimu*';
      }
    }

    final lowerMessage = message.toLowerCase();

    // Detekce záměru (intent detection)
    if (_containsAny(lowerMessage, ['ahoj', 'dobrý den', 'zdravím', 'čau'])) {
      return _getRandomResponse('greeting');
    }

    if (_containsAny(lowerMessage, [
      'restaurace',
      'jídlo',
      'večeře',
      'oběd',
      'snídaně',
    ])) {
      return _getRandomResponse('restaurants');
    }

    if (_containsAny(lowerMessage, [
      'památky',
      'atrakce',
      'místa',
      'navštívit',
      'vidět',
    ])) {
      return _getRandomResponse('attractions');
    }

    if (_containsAny(lowerMessage, ['počasí', 'teplota', 'déšť', 'slunce'])) {
      return _getRandomResponse('weather');
    }

    if (_containsAny(lowerMessage, [
      'pomoc',
      'nouzová',
      'emergency',
      'policie',
      'lékař',
    ])) {
      return _getRandomResponse('emergency');
    }

    // Specifické dotazy
    if (_containsAny(lowerMessage, ['dubrovník', 'dubrovnik'])) {
      return 'Dubrovník je nádherné historické město na jihu Chorvatska. Doporučuji procházku po hradbách, návštěvu Starého města a výlet lanovkou na horu Srđ pro úžasný výhled.';
    }

    if (_containsAny(lowerMessage, ['split'])) {
      return 'Split je druhé největší město Chorvatska s úžasným Diokleciánovým palácem. Nezapomeňte navštívit Riva waterfront a vydat se na výlet na ostrov Brač nebo Hvar.';
    }

    if (_containsAny(lowerMessage, ['plitvice', 'plitvická'])) {
      return 'Plitvická jezera jsou národní park UNESCO s kaskádami jezer a vodopádů. Nejlepší čas návštěvy je od dubna do října. Doporučuji celý den a pohodlnou obuv.';
    }

    // Výchozí odpověď
    return 'To je zajímavý dotaz! Můžete mi říct více detailů? Rád vám pomohu s informacemi o Chorvatsku, doporučením míst, restaurací nebo plánováním výletů.';
  }

  /// Kontroluje, zda text obsahuje některé z klíčových slov
  bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }

  /// Vrátí náhodnou odpověď z kategorie
  String _getRandomResponse(String category) {
    final responses =
        _knowledgeBase[category] ?? ['Omlouvám se, nevím jak odpovědět.'];
    final random = Random();
    return responses[random.nextInt(responses.length)];
  }

  /// Spustí hlasové rozpoznávání
  Future<void> startListening() async {
    if (!_speechToText.isAvailable || _isListening) return;

    try {
      _isListening = true;
      await _speechToText.listen(
        onResult: (result) async {
          if (result.finalResult) {
            _isListening = false;
            await processTextMessage(result.recognizedWords);
          }
        },
        listenFor: const Duration(seconds: 30),
        pauseFor: const Duration(seconds: 3),
        listenOptions: SpeechListenOptions(partialResults: true),
        localeId: 'cs_CZ',
      );
    } catch (e) {
      _isListening = false;
      debugPrint('Chyba při hlasovém rozpoznávání: $e');
    }
  }

  /// Zastaví hlasové rozpoznávání
  Future<void> stopListening() async {
    if (_isListening) {
      await _speechToText.stop();
      _isListening = false;
    }
  }

  /// Přečte text nahlas
  Future<void> speak(String text) async {
    if (_isSpeaking) return;

    try {
      _isSpeaking = true;
      await _flutterTts.speak(text);
      _isSpeaking = false;
    } catch (e) {
      _isSpeaking = false;
      debugPrint('Chyba při syntéze řeči: $e');
    }
  }

  /// Zastaví mluvení
  Future<void> stopSpeaking() async {
    if (_isSpeaking) {
      await _flutterTts.stop();
      _isSpeaking = false;
    }
  }

  /// Rozpoznání památky z obrázku
  Future<assistant.AIMessage> recognizeMonument(String imagePath) async {
    try {
      final response = await _orchestrator.recognizeMonument(imagePath);

      final aiMessage = assistant.AIMessage(
        id: _generateId(),
        conversationId: _getCurrentConversationId(),
        sender: assistant.MessageSender.ai,
        content: response.content,
        type: assistant.MessageType.text,
        timestamp: DateTime.now(),
        isRead: false,
        metadata: assistant.AIMessageMetadata(
          confidence: response.confidence,
          language: 'cs',
        ),
      );

      _addMessageToCurrentConversation(aiMessage);
      return aiMessage;
    } catch (e) {
      debugPrint('Chyba při rozpoznávání památky: $e');
      return _createErrorMessage();
    }
  }

  /// Získání personalizovaných doporučení
  Future<List<orchestrator.AIRecommendation>> getPersonalizedRecommendations({
    int limit = 10,
  }) async {
    try {
      return await _orchestrator.getPersonalizedRecommendations(limit: limit);
    } catch (e) {
      debugPrint('Chyba při získávání doporučení: $e');
      return [];
    }
  }

  /// Převod řeči na text
  Future<String> convertSpeechToText(String audioPath) async {
    try {
      // Implementace převodu řeči na text
      // Zde by byla integrace s cloud speech-to-text API
      return 'Převedený text z audio souboru';
    } catch (e) {
      debugPrint('Chyba při převodu řeči na text: $e');
      return '';
    }
  }

  /// Generování lokální odpovědi (pro orchestrátor)
  Future<orchestrator.AIResponse> generateLocalResponse(
    String query,
    orchestrator.AIContext context,
  ) async {
    try {
      final response = await _generateFallbackResponse(query);

      return orchestrator.AIResponse(
        content: response,
        type: orchestrator.AIResponseType.text,
        confidence: 0.7,
        source: orchestrator.AIResponseSource.local,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Chyba při generování lokální odpovědi: $e');
      return orchestrator.AIResponse(
        content: 'Omlouvám se, došlo k chybě.',
        type: orchestrator.AIResponseType.error,
        confidence: 0.0,
        source: orchestrator.AIResponseSource.fallback,
        timestamp: DateTime.now(),
      );
    }
  }

  /// Vytvoří novou konverzaci
  assistant.AIConversation createNewConversation() {
    final conversation = assistant.AIConversation(
      id: _generateId(),
      userId: 'current_user', // TODO: Implementovat správu uživatelů
      title: 'Nová konverzace',
      messages: [],
      context:
          _currentContext ??
          assistant.ConversationContext(userPreferences: [], sessionData: {}),
      createdAt: DateTime.now(),
      lastMessageAt: DateTime.now(),
      isActive: true,
    );

    _conversations.add(conversation);
    _currentConversation = conversation;
    return conversation;
  }

  /// Pomocné metody
  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  String _getCurrentConversationId() {
    return _currentConversation?.id ?? createNewConversation().id;
  }

  void _addMessageToCurrentConversation(assistant.AIMessage message) {
    if (_currentConversation == null) {
      createNewConversation();
    }
    _currentConversation!.addMessage(message);
  }

  assistant.AIMessage _createErrorMessage() {
    return assistant.AIMessage(
      id: _generateId(),
      conversationId: _getCurrentConversationId(),
      sender: assistant.MessageSender.ai,
      content: 'Omlouvám se, došlo k chybě. Zkuste to prosím znovu.',
      type: assistant.MessageType.text,
      timestamp: DateTime.now(),
      isRead: false,
    );
  }

  // Gettery
  List<assistant.AIConversation> get conversations => _conversations;
  assistant.AIConversation? get currentConversation => _currentConversation;
  bool get isListening => _isListening;
  bool get isSpeaking => _isSpeaking;
  bool get isAvailable => _speechToText.isAvailable;
}
