import 'package:flutter/material.dart';
import '../theme/watercolor_theme.dart';
import 'business_registration_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      // Watercolor pozadí
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xFFF5F3F0), // Světlá písková
              Color(0xFFE8E3DD), // Teplá b<PERSON>
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // TOP NAVIGATION BAR - minimalistický
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Menu tla<PERSON>tko
                    _buildTopNavButton(
                      icon: Icons.menu,
                      onTap: () => _showMainMenu(context),
                      semanticLabel: 'Otevřít hlavní menu',
                    ),

                    // Adriatic Diary title
                    Semantics(
                      header: true,
                      child: Text(
                        'Adriatic Diary',
                        style: WatercolorTheme.headingMedium.copyWith(
                          color: WatercolorTheme.adriaticBlue,
                        ),
                      ),
                    ),

                    // Right side buttons
                    Row(
                      children: [
                        // AI Asistent
                        _buildTopNavButton(
                          icon: Icons.psychology,
                          onTap: () =>
                              _showFeatureComingSoon(context, 'AI Asistent'),
                          semanticLabel: 'AI Asistent',
                        ),
                        const SizedBox(width: 12),
                        // Profil
                        _buildTopNavButton(
                          icon: Icons.person,
                          onTap: () =>
                              _showFeatureComingSoon(context, 'Profil'),
                          semanticLabel: 'Uživatelský profil',
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // HLAVNÍ DENÍKOVÝ INTERFACE - responsivní
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // Responsivní padding podle velikosti obrazovky
                    final isLargeScreen = constraints.maxWidth > 600;
                    final horizontalMargin = isLargeScreen ? 40.0 : 20.0;
                    final cardPadding = isLargeScreen ? 32.0 : 24.0;

                    return Container(
                      margin: EdgeInsets.symmetric(
                        horizontal: horizontalMargin,
                        vertical: 20,
                      ),
                      padding: EdgeInsets.all(cardPadding),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: WatercolorTheme.adriaticBlue.withValues(
                              alpha: 0.1,
                            ),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Deníkový zápis header
                          Text(
                            'Deníkový zápis',
                            style: WatercolorTheme.headingMedium.copyWith(
                              color: WatercolorTheme.adriaticBlue,
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Voice input area
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: WatercolorTheme.adriaticBlue.withValues(
                                alpha: 0.05,
                              ),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: WatercolorTheme.adriaticBlue.withValues(
                                  alpha: 0.2,
                                ),
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: WatercolorTheme.sunsetOrange,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: const Icon(
                                    Icons.mic,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Text(
                                    'Naslouchá...',
                                    style: WatercolorTheme.bodyLarge.copyWith(
                                      color: WatercolorTheme.adriaticBlue,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Text input area - interaktivní
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFAF9F7),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: WatercolorTheme.adriaticBlue
                                      .withValues(alpha: 0.1),
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: TextField(
                                      maxLines: null,
                                      expands: true,
                                      decoration: InputDecoration(
                                        hintText: 'Napište shrnutí dne...',
                                        hintStyle: WatercolorTheme.bodyMedium
                                            .copyWith(
                                              color: Colors.grey[600],
                                              fontStyle: FontStyle.italic,
                                            ),
                                        border: InputBorder.none,
                                        contentPadding: EdgeInsets.zero,
                                      ),
                                      style: WatercolorTheme.bodyLarge.copyWith(
                                        height: 1.6,
                                      ),
                                      textAlignVertical: TextAlignVertical.top,
                                    ),
                                  ),
                                  // Sample text jako ukázka
                                  Container(
                                    padding: const EdgeInsets.only(top: 12),
                                    child: Text(
                                      'Ukázka: "Dnes jsme jeli trajektem na ostrov Hvar a měli jsme úžasný den na pláži."',
                                      style: WatercolorTheme.bodySmall.copyWith(
                                        color: Colors.grey[500],
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Mood tags
                          Wrap(
                            spacing: 12,
                            runSpacing: 8,
                            children: [
                              _buildMoodTag(
                                'Šťastný',
                                WatercolorTheme.sunsetOrange,
                                true,
                              ),
                              _buildMoodTag(
                                'Veselý',
                                WatercolorTheme.mediterraneanTeal,
                                false,
                              ),
                              _buildMoodTag(
                                'Pokojný',
                                WatercolorTheme.adriaticBlue,
                                false,
                              ),
                              _buildMoodTag(
                                'Nadšený',
                                WatercolorTheme.sunsetOrange,
                                false,
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Top navigation button s accessibility
  Widget _buildTopNavButton({
    required IconData icon,
    required VoidCallback onTap,
    String? semanticLabel,
  }) {
    return Semantics(
      button: true,
      label: semanticLabel,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: WatercolorTheme.adriaticBlue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: WatercolorTheme.adriaticBlue, size: 24),
        ),
      ),
    );
  }

  // Mood tag widget s optimalizací
  Widget _buildMoodTag(String label, Color color, bool isSelected) {
    // Cache border radius pro performance
    const borderRadius = BorderRadius.all(Radius.circular(20));

    return InkWell(
      onTap: () {
        setState(() {
          // Implementace mood selection
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Vybrali jste náladu: $label'),
              duration: const Duration(seconds: 1),
              behavior: SnackBarBehavior.floating,
            ),
          );
        });
      },
      borderRadius: borderRadius,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? color : color.withValues(alpha: 0.1),
          borderRadius: borderRadius,
          border: Border.all(color: color, width: isSelected ? 0 : 1),
        ),
        child: Text(
          label,
          style: WatercolorTheme.labelMedium.copyWith(
            color: isSelected ? Colors.white : color,
          ),
        ),
      ),
    );
  }

  // Main menu
  void _showMainMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text('Glavni meni', style: WatercolorTheme.headingMedium),
            ),

            // Menu items
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                children: [
                  _buildMenuItem(Icons.map, 'Karta', () {
                    Navigator.pop(context);
                    DefaultTabController.of(context).animateTo(1);
                  }),
                  _buildMenuItem(Icons.place, 'Mjesta', () {
                    Navigator.pop(context);
                    DefaultTabController.of(context).animateTo(2);
                  }),
                  _buildMenuItem(Icons.camera_alt, 'Fotografije', () {
                    Navigator.pop(context);
                    _showFeatureComingSoon(context, 'Kamera');
                  }),
                  _buildMenuItem(Icons.business, 'Posao', () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            const BusinessRegistrationScreen(),
                      ),
                    );
                  }),
                  _buildMenuItem(Icons.analytics, 'Analitika', () {
                    Navigator.pop(context);
                    _showFeatureComingSoon(context, 'Analitika');
                  }),
                  _buildMenuItem(Icons.wallet, 'Novčanik', () {
                    Navigator.pop(context);
                    _showFeatureComingSoon(context, 'Novčanik');
                  }),
                  _buildMenuItem(Icons.traffic, 'Promet', () {
                    Navigator.pop(context);
                    _showFeatureComingSoon(context, 'Prometne kamere');
                  }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Menu item
  Widget _buildMenuItem(IconData icon, String title, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: WatercolorTheme.adriaticBlue),
      title: Text(title, style: WatercolorTheme.bodyLarge),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  void _showFeatureComingSoon(BuildContext context, String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.construction, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                '$feature uskoro stiže! 🚀',
                style: WatercolorTheme.bodyMedium.copyWith(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: WatercolorTheme.adriaticBlue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
