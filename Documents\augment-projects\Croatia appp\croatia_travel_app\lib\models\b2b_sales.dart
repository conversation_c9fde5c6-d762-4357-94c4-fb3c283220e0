import 'package:json_annotation/json_annotation.dart';

part 'b2b_sales.g.dart';

/// 🏢 B2B Sales Models
/// Modely pro B2B prodej a správu klientů

@JsonSerializable()
class B2BLead {
  final String id;
  final String companyName;
  final String contactPerson;
  final String email;
  final String phone;
  final B2BSegment segment;
  final LeadSource source;
  final LeadStatus status;
  final double estimatedValue;
  final DateTime createdAt;
  final DateTime? lastContactAt;
  final String? notes;
  final UrgencyLevel urgency;
  final Map<String, dynamic> customFields;
  final LeadQualification? qualification;
  final double? score;

  const B2BLead({
    required this.id,
    required this.companyName,
    required this.contactPerson,
    required this.email,
    required this.phone,
    required this.segment,
    required this.source,
    required this.status,
    required this.estimatedValue,
    required this.createdAt,
    this.lastContactAt,
    this.notes,
    this.urgency = UrgencyLevel.medium,
    this.customFields = const {},
    this.qualification,
    this.score,
  });

  factory B2BLead.fromJson(Map<String, dynamic> json) =>
      _$B2BLeadFromJson(json);
  Map<String, dynamic> toJson() => _$B2BLeadToJson(this);

  B2BLead copyWith({
    String? id,
    String? companyName,
    String? contactPerson,
    String? email,
    String? phone,
    B2BSegment? segment,
    LeadSource? source,
    LeadStatus? status,
    double? estimatedValue,
    DateTime? createdAt,
    DateTime? lastContactAt,
    String? notes,
    UrgencyLevel? urgency,
    Map<String, dynamic>? customFields,
    LeadQualification? qualification,
    double? score,
  }) {
    return B2BLead(
      id: id ?? this.id,
      companyName: companyName ?? this.companyName,
      contactPerson: contactPerson ?? this.contactPerson,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      segment: segment ?? this.segment,
      source: source ?? this.source,
      status: status ?? this.status,
      estimatedValue: estimatedValue ?? this.estimatedValue,
      createdAt: createdAt ?? this.createdAt,
      lastContactAt: lastContactAt ?? this.lastContactAt,
      notes: notes ?? this.notes,
      urgency: urgency ?? this.urgency,
      customFields: customFields ?? this.customFields,
      qualification: qualification ?? this.qualification,
      score: score ?? this.score,
    );
  }
}

@JsonSerializable()
class B2BProposal {
  final String id;
  final String leadId;
  final String title;
  final String description;
  final double totalValue;
  final B2BPackage package;
  final ProposalStatus status;
  final DateTime createdAt;
  final DateTime? sentAt;
  final DateTime? validUntil;
  final List<ProposalItem> items;
  final String? terms;
  final String? companyName;
  final double? customPrice;
  final int? contractLength;

  const B2BProposal({
    required this.id,
    required this.leadId,
    required this.title,
    required this.description,
    required this.totalValue,
    required this.package,
    required this.status,
    required this.createdAt,
    this.sentAt,
    this.validUntil,
    this.items = const [],
    this.terms,
    this.companyName,
    this.customPrice,
    this.contractLength,
  });

  factory B2BProposal.fromJson(Map<String, dynamic> json) =>
      _$B2BProposalFromJson(json);
  Map<String, dynamic> toJson() => _$B2BProposalToJson(this);

  B2BProposal copyWith({
    String? id,
    String? leadId,
    String? title,
    String? description,
    double? totalValue,
    B2BPackage? package,
    ProposalStatus? status,
    DateTime? createdAt,
    DateTime? sentAt,
    DateTime? validUntil,
    List<ProposalItem>? items,
    String? terms,
    String? companyName,
    double? customPrice,
    int? contractLength,
  }) {
    return B2BProposal(
      id: id ?? this.id,
      leadId: leadId ?? this.leadId,
      title: title ?? this.title,
      description: description ?? this.description,
      totalValue: totalValue ?? this.totalValue,
      package: package ?? this.package,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      sentAt: sentAt ?? this.sentAt,
      validUntil: validUntil ?? this.validUntil,
      items: items ?? this.items,
      terms: terms ?? this.terms,
      companyName: companyName ?? this.companyName,
      customPrice: customPrice ?? this.customPrice,
      contractLength: contractLength ?? this.contractLength,
    );
  }
}

@JsonSerializable()
class B2BContract {
  final String id;
  final String proposalId;
  final String clientId;
  final String title;
  final double value;
  final ContractStatus status;
  final DateTime startDate;
  final DateTime endDate;
  final PaymentTerms paymentTerms;
  final List<ContractMilestone> milestones;
  final String? signedDocumentUrl;

  const B2BContract({
    required this.id,
    required this.proposalId,
    required this.clientId,
    required this.title,
    required this.value,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.paymentTerms,
    this.milestones = const [],
    this.signedDocumentUrl,
  });

  factory B2BContract.fromJson(Map<String, dynamic> json) =>
      _$B2BContractFromJson(json);
  Map<String, dynamic> toJson() => _$B2BContractToJson(this);
}

@JsonSerializable()
class B2BClient {
  final String id;
  final String companyName;
  final String industry;
  final int employeeCount;
  final double annualRevenue;
  final String primaryContact;
  final String email;
  final String phone;
  final String address;
  final ClientTier tier;
  final DateTime onboardedAt;
  final List<String> contractIds;

  const B2BClient({
    required this.id,
    required this.companyName,
    required this.industry,
    required this.employeeCount,
    required this.annualRevenue,
    required this.primaryContact,
    required this.email,
    required this.phone,
    required this.address,
    required this.tier,
    required this.onboardedAt,
    this.contractIds = const [],
  });

  factory B2BClient.fromJson(Map<String, dynamic> json) =>
      _$B2BClientFromJson(json);
  Map<String, dynamic> toJson() => _$B2BClientToJson(this);
}

@JsonSerializable()
class SalesMetrics {
  final double totalRevenue;
  final int totalLeads;
  final int convertedLeads;
  final double conversionRate;
  final double averageDealSize;
  final int activePipeline;
  final Map<String, double> revenueBySegment;
  final Map<String, int> leadsBySource;
  final DateTime periodStart;
  final DateTime periodEnd;

  const SalesMetrics({
    required this.totalRevenue,
    required this.totalLeads,
    required this.convertedLeads,
    required this.conversionRate,
    required this.averageDealSize,
    required this.activePipeline,
    required this.revenueBySegment,
    required this.leadsBySource,
    required this.periodStart,
    required this.periodEnd,
  });

  factory SalesMetrics.fromJson(Map<String, dynamic> json) =>
      _$SalesMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$SalesMetricsToJson(this);
}

@JsonSerializable()
class SalesEvent {
  final String id;
  final SalesEventType type;
  final String leadId;
  final String description;
  final DateTime timestamp;
  final String userId;
  final Map<String, dynamic> metadata;

  const SalesEvent({
    required this.id,
    required this.type,
    required this.leadId,
    required this.description,
    required this.timestamp,
    required this.userId,
    this.metadata = const {},
  });

  factory SalesEvent.fromJson(Map<String, dynamic> json) =>
      _$SalesEventFromJson(json);
  Map<String, dynamic> toJson() => _$SalesEventToJson(this);
}

// Enums
enum B2BSegment {
  startup,
  smallBusiness,
  mediumBusiness,
  enterprise,
  government,
  tourismBoard,
  travelAgency,
  hotel,
  restaurant,
  eventOrganizer,
}

enum LeadSource {
  website,
  referral,
  coldOutreach,
  socialMedia,
  event,
  partnership,
  advertising,
  inbound,
}

enum LeadStatus {
  new_,
  contacted,
  qualified,
  proposal,
  negotiation,
  won,
  lost,
  disqualified,
  closed,
}

enum UrgencyLevel { low, medium, high, critical }

enum ProposalStatus { draft, sent, viewed, accepted, rejected, expired }

enum ContractStatus { draft, active, completed, cancelled, suspended }

enum ClientTier { bronze, silver, gold, platinum }

enum SalesEventType {
  leadCreated,
  leadContacted,
  proposalSent,
  contractSigned,
  meetingScheduled,
  followUpRequired,
  leadQualified,
  leadDisqualified,
  proposalCreated,
  dealClosed,
}

// Supporting classes
@JsonSerializable()
class B2BPackage {
  final String id;
  final String name;
  final String description;
  final double price;
  final List<String> features;
  final double monthlyPrice;

  const B2BPackage({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.features,
    required this.monthlyPrice,
  });

  factory B2BPackage.fromJson(Map<String, dynamic> json) =>
      _$B2BPackageFromJson(json);
  Map<String, dynamic> toJson() => _$B2BPackageToJson(this);
}

@JsonSerializable()
class ProposalItem {
  final String name;
  final String description;
  final int quantity;
  final double unitPrice;
  final double totalPrice;

  const ProposalItem({
    required this.name,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
  });

  factory ProposalItem.fromJson(Map<String, dynamic> json) =>
      _$ProposalItemFromJson(json);
  Map<String, dynamic> toJson() => _$ProposalItemToJson(this);
}

@JsonSerializable()
class PaymentTerms {
  final int paymentDays;
  final String currency;
  final PaymentMethod method;
  final bool isRecurring;
  final int? recurringMonths;

  const PaymentTerms({
    required this.paymentDays,
    required this.currency,
    required this.method,
    required this.isRecurring,
    this.recurringMonths,
  });

  factory PaymentTerms.fromJson(Map<String, dynamic> json) =>
      _$PaymentTermsFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentTermsToJson(this);
}

@JsonSerializable()
class ContractMilestone {
  final String id;
  final String title;
  final String description;
  final DateTime dueDate;
  final double value;
  final bool isCompleted;
  final DateTime? completedAt;

  const ContractMilestone({
    required this.id,
    required this.title,
    required this.description,
    required this.dueDate,
    required this.value,
    required this.isCompleted,
    this.completedAt,
  });

  factory ContractMilestone.fromJson(Map<String, dynamic> json) =>
      _$ContractMilestoneFromJson(json);
  Map<String, dynamic> toJson() => _$ContractMilestoneToJson(this);
}

@JsonSerializable()
class LeadQualification {
  final String leadId;
  final int budgetScore;
  final int authorityScore;
  final int needScore;
  final int timelineScore;
  final int totalScore;
  final bool isQualified;
  final String? notes;
  final double? budget;
  final int? employeeCount;
  final bool? hasDecisionMaker;
  final UrgencyLevel? urgency;
  final DateTime? qualifiedAt;
  final String? qualifiedBy;

  const LeadQualification({
    required this.leadId,
    required this.budgetScore,
    required this.authorityScore,
    required this.needScore,
    required this.timelineScore,
    required this.totalScore,
    required this.isQualified,
    this.notes,
    this.budget,
    this.employeeCount,
    this.hasDecisionMaker,
    this.urgency,
    this.qualifiedAt,
    this.qualifiedBy,
  });

  factory LeadQualification.fromJson(Map<String, dynamic> json) =>
      _$LeadQualificationFromJson(json);
  Map<String, dynamic> toJson() => _$LeadQualificationToJson(this);
}

enum PaymentMethod { bankTransfer, creditCard, paypal, crypto, check }

enum ReportPeriod {
  daily,
  weekly,
  monthly,
  quarterly,
  yearly,
  custom,
  thisMonth,
  lastMonth,
  thisQuarter,
  thisYear,
}

@JsonSerializable()
class SalesDashboard {
  final DateTime generatedAt;
  final ReportPeriod period;
  final double totalRevenue;
  final int totalLeads;
  final int convertedLeads;
  final double conversionRate;
  final int activeProposals;
  final int wonDeals;
  final int lostDeals;
  final double averageDealSize;
  final Map<String, double> revenueBySegment;
  final Map<String, int> leadsBySource;
  final List<B2BLead> topLeads;
  final List<B2BProposal> recentProposals;

  const SalesDashboard({
    required this.generatedAt,
    required this.period,
    required this.totalRevenue,
    required this.totalLeads,
    required this.convertedLeads,
    required this.conversionRate,
    required this.activeProposals,
    required this.wonDeals,
    required this.lostDeals,
    required this.averageDealSize,
    required this.revenueBySegment,
    required this.leadsBySource,
    required this.topLeads,
    required this.recentProposals,
  });

  factory SalesDashboard.fromJson(Map<String, dynamic> json) =>
      _$SalesDashboardFromJson(json);
  Map<String, dynamic> toJson() => _$SalesDashboardToJson(this);
}

enum SalesMetricType { revenue, leads, conversion, deals, pipeline, forecast }

@JsonSerializable()
class SalesReport {
  final String id;
  final ReportPeriod period;
  final DateTime fromDate;
  final DateTime toDate;
  final List<SalesMetricType> metrics;
  final SalesDashboard dashboard;
  final List<String> insights;
  final List<String> recommendations;
  final DateTime generatedAt;

  const SalesReport({
    required this.id,
    required this.period,
    required this.fromDate,
    required this.toDate,
    required this.metrics,
    required this.dashboard,
    required this.insights,
    required this.recommendations,
    required this.generatedAt,
  });

  factory SalesReport.fromJson(Map<String, dynamic> json) =>
      _$SalesReportFromJson(json);
  Map<String, dynamic> toJson() => _$SalesReportToJson(this);
}

@JsonSerializable()
class SalesForecast {
  final DateTime month;
  final double projectedRevenue;
  final double confidence;
  final List<String> factors;

  const SalesForecast({
    required this.month,
    required this.projectedRevenue,
    required this.confidence,
    required this.factors,
  });

  factory SalesForecast.fromJson(Map<String, dynamic> json) =>
      _$SalesForecastFromJson(json);
  Map<String, dynamic> toJson() => _$SalesForecastToJson(this);
}
