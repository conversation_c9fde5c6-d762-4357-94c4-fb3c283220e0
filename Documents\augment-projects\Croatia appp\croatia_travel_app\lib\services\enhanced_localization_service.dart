import 'dart:async';
import 'dart:convert';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 🌍 ENHANCED LOCALIZATION SERVICE - Pokročilá správa jazyků
class EnhancedLocalizationService {
  static final EnhancedLocalizationService _instance =
      EnhancedLocalizationService._internal();
  factory EnhancedLocalizationService() => _instance;
  EnhancedLocalizationService._internal();

  bool _isInitialized = false;
  Locale _currentLocale = const Locale('hr', 'HR'); // Chorvatština jako výchozí
  final Map<String, Map<String, String>> _localizedStrings = {};
  final StreamController<Locale> _localeController =
      StreamController.broadcast();

  /// Stream změn jazyka
  Stream<Locale> get localeChanges => _localeController.stream;

  /// Podporované jazyky - Chorvatština jako primární
  static const List<Locale> supportedLocales = [
    Locale('hr', 'HR'), // Chorvatština - primární jazyk
    Locale('en', 'US'), // Angličtina - pro turisty
    Locale('de', 'DE'), // Němčina - pro německé turisty
    Locale('it', 'IT'), // Italština - pro italské turisty
  ];

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🌍 Inicializuji Enhanced Localization Service...');

      // Načtení uloženého jazyka
      await _loadSavedLocale();

      // Načtení všech jazykových souborů
      await _loadAllLocalizations();

      _isInitialized = true;
      debugPrint(
        '✅ Enhanced Localization Service inicializován - jazyk: ${_currentLocale.languageCode}',
      );
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Enhanced Localization Service: $e');
      rethrow;
    }
  }

  /// Získání aktuálního jazyka
  Locale get currentLocale => _currentLocale;

  /// Změna jazyka
  Future<void> setLocale(Locale locale) async {
    if (!supportedLocales.contains(locale)) {
      debugPrint('❌ Nepodporovaný jazyk: ${locale.languageCode}');
      return;
    }

    try {
      _currentLocale = locale;

      // Uložení do preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('enhanced_locale', locale.languageCode);

      // Notifikace o změně
      _localeController.add(locale);

      debugPrint('✅ Jazyk změněn na: ${locale.languageCode}');
    } catch (e) {
      debugPrint('❌ Chyba při změně jazyka: $e');
    }
  }

  /// Získání lokalizovaného textu
  String getText(String key, {Map<String, dynamic>? args}) {
    try {
      final languageCode = _currentLocale.languageCode;
      final localizedMap = _localizedStrings[languageCode];

      if (localizedMap == null) {
        debugPrint('❌ Jazykový soubor nenalezen pro: $languageCode');
        return _getFallbackText(key, args);
      }

      String? text = localizedMap[key];

      if (text == null) {
        debugPrint('❌ Text nenalezen pro klíč: $key');
        return _getFallbackText(key, args);
      }

      // Nahrazení parametrů
      if (args != null) {
        args.forEach((paramKey, value) {
          text = text!.replaceAll('{$paramKey}', value.toString());
        });
      }

      return text!;
    } catch (e) {
      debugPrint('❌ Chyba při získávání textu pro klíč $key: $e');
      return _getFallbackText(key, args);
    }
  }

  /// Získání textu s fallback na angličtinu
  String _getFallbackText(String key, Map<String, dynamic>? args) {
    try {
      // Pokus o angličtinu jako fallback
      final englishMap = _localizedStrings['en'];
      if (englishMap != null && englishMap.containsKey(key)) {
        String text = englishMap[key]!;

        if (args != null) {
          args.forEach((paramKey, value) {
            text = text.replaceAll('{$paramKey}', value.toString());
          });
        }

        return text;
      }

      // Pokud ani angličtina není dostupná, vrátíme klíč
      return key;
    } catch (e) {
      return key;
    }
  }

  /// Detekce jazyka podle systému
  Locale detectSystemLocale() {
    final systemLocale = PlatformDispatcher.instance.locale;

    // Kontrola, zda je systémový jazyk podporován
    for (final supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == systemLocale.languageCode) {
        return supportedLocale;
      }
    }

    // Fallback na chorvatštinu (primární jazyk aplikace)
    return const Locale('hr', 'HR');
  }

  /// Získání názvu jazyka v daném jazyce
  String getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'hr':
        return getText('languageCroatian');
      case 'en':
        return getText('languageEnglish');
      case 'de':
        return getText('languageGerman');
      case 'it':
        return getText('languageItalian');
      default:
        return languageCode.toUpperCase();
    }
  }

  /// Získání seznamu dostupných jazyků
  List<Map<String, String>> getAvailableLanguages() {
    return supportedLocales
        .map(
          (locale) => {
            'code': locale.languageCode,
            'name': getLanguageName(locale.languageCode),
            'nativeName': _getNativeLanguageName(locale.languageCode),
            'flag': _getLanguageFlag(locale.languageCode),
          },
        )
        .toList();
  }

  /// Získání názvu jazyka v jeho rodném jazyce
  String _getNativeLanguageName(String languageCode) {
    switch (languageCode) {
      case 'hr':
        return 'Hrvatski';
      case 'en':
        return 'English';
      case 'de':
        return 'Deutsch';
      case 'it':
        return 'Italiano';
      default:
        return languageCode.toUpperCase();
    }
  }

  /// Získání emoji vlajky pro jazyk
  String _getLanguageFlag(String languageCode) {
    switch (languageCode) {
      case 'hr':
        return '🇭🇷';
      case 'en':
        return '🇺🇸';
      case 'de':
        return '🇩🇪';
      case 'it':
        return '🇮🇹';
      default:
        return '🌍';
    }
  }

  /// Formátování čísel podle lokalizace
  String formatNumber(num number, {int? decimalPlaces}) {
    try {
      switch (_currentLocale.languageCode) {
        case 'hr':
        case 'de':
        case 'it':
          // Evropské formátování: 1.234,56
          return _formatNumberEuropean(number, decimalPlaces);
        default:
          // Anglické formátování: 1,234.56
          return _formatNumberEnglish(number, decimalPlaces);
      }
    } catch (e) {
      return number.toString();
    }
  }

  /// Formátování měny
  String formatCurrency(num amount, {String? currencyCode}) {
    final currency = currencyCode ?? _getDefaultCurrency();
    final formattedAmount = formatNumber(amount, decimalPlaces: 2);

    switch (currency) {
      case 'EUR':
        return '$formattedAmount €';
      case 'USD':
        return '\$$formattedAmount';
      case 'GBP':
        return '£$formattedAmount';
      case 'HRK':
        return '$formattedAmount kn';
      default:
        return '$formattedAmount $currency';
    }
  }

  /// Získání výchozí měny podle jazyka
  String _getDefaultCurrency() {
    switch (_currentLocale.languageCode) {
      case 'hr':
        return 'EUR'; // Chorvatsko používá EUR od 2023
      case 'en':
        return 'USD';
      case 'de':
      case 'it':
        return 'EUR';
      default:
        return 'EUR';
    }
  }

  /// Formátování data podle lokalizace
  String formatDate(DateTime date, {String? format}) {
    try {
      switch (_currentLocale.languageCode) {
        case 'hr':
        case 'de':
          return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}.${date.year}';
        case 'it':
          return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
        default:
          return '${date.month.toString().padLeft(2, '0')}/${date.day.toString().padLeft(2, '0')}/${date.year}';
      }
    } catch (e) {
      return date.toString();
    }
  }

  /// Načtení uloženého jazyka
  Future<void> _loadSavedLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLanguage = prefs.getString('enhanced_locale');

      if (savedLanguage != null) {
        final savedLocale = supportedLocales.firstWhere(
          (locale) => locale.languageCode == savedLanguage,
          orElse: () => detectSystemLocale(),
        );
        _currentLocale = savedLocale;
      } else {
        // Pokud není uložený jazyk, detekujeme systémový
        _currentLocale = detectSystemLocale();
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání uloženého jazyka: $e');
      _currentLocale = const Locale('hr', 'HR'); // Fallback na chorvatštinu
    }
  }

  /// Načtení všech lokalizačních souborů
  Future<void> _loadAllLocalizations() async {
    for (final locale in supportedLocales) {
      await _loadLocalization(locale.languageCode);
    }
  }

  /// Načtení konkrétního lokalizačního souboru
  Future<void> _loadLocalization(String languageCode) async {
    try {
      final jsonString = await rootBundle.loadString(
        'lib/l10n/app_$languageCode.arb',
      );
      final Map<String, dynamic> jsonMap = json.decode(jsonString);

      // Filtrování pouze textových klíčů (bez meta informací)
      final Map<String, String> localizedStrings = {};
      jsonMap.forEach((key, value) {
        if (!key.startsWith('@@') &&
            !key.startsWith('_comment_') &&
            value is String) {
          localizedStrings[key] = value;
        }
      });

      _localizedStrings[languageCode] = localizedStrings;
      debugPrint(
        '✅ Načten jazykový soubor: $languageCode (${localizedStrings.length} textů)',
      );
    } catch (e) {
      debugPrint('❌ Chyba při načítání jazykového souboru $languageCode: $e');
      // Fallback na základní texty
      _loadFallbackStrings(languageCode);
    }
  }

  /// Načtení fallback textů pokud soubor neexistuje
  void _loadFallbackStrings(String languageCode) {
    switch (languageCode) {
      case 'hr':
        _localizedStrings['hr'] = {
          'appTitle': 'Croatia Travel App',
          'navHome': 'Početna',
          'navDiary': 'Dnevnik',
          'navLife': 'Život',
          'navWallet': 'Novčanik',
          'navCommunity': 'Zajednica',
          'navProfile': 'Profil',
          'actionSave': 'Spremi',
          'actionCancel': 'Odustani',
          'actionDelete': 'Obriši',
          'actionEdit': 'Uredi',
          'actionShare': 'Podijeli',
          'errorNoInternet': 'Nema internetske veze',
          'successSaved': 'Uspješno spremljeno',
        };
        break;
      case 'en':
        _localizedStrings['en'] = {
          'appTitle': 'Croatia Travel App',
          'navHome': 'Home',
          'navDiary': 'Diary',
          'navLife': 'Life',
          'navWallet': 'Wallet',
          'navCommunity': 'Community',
          'navProfile': 'Profile',
          'actionSave': 'Save',
          'actionCancel': 'Cancel',
          'actionDelete': 'Delete',
          'actionEdit': 'Edit',
          'actionShare': 'Share',
          'errorNoInternet': 'No internet connection',
          'successSaved': 'Successfully saved',
        };
        break;
    }
  }

  /// Formátování čísel pro různé jazyky
  String _formatNumberEuropean(num number, int? decimalPlaces) {
    // Evropské formátování: 1.234,56
    final parts = number.toStringAsFixed(decimalPlaces ?? 0).split('.');
    final integerPart = parts[0].replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]}.',
    );

    if (decimalPlaces != null && decimalPlaces > 0 && parts.length > 1) {
      return '$integerPart,${parts[1]}';
    }
    return integerPart;
  }

  String _formatNumberEnglish(num number, int? decimalPlaces) {
    // Anglické formátování: 1,234.56
    final parts = number.toStringAsFixed(decimalPlaces ?? 0).split('.');
    final integerPart = parts[0].replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );

    if (decimalPlaces != null && decimalPlaces > 0 && parts.length > 1) {
      return '$integerPart.${parts[1]}';
    }
    return integerPart;
  }

  void dispose() {
    _localeController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  Map<String, Map<String, String>> get localizedStrings =>
      Map.unmodifiable(_localizedStrings);
}

/// Extension pro snadné používání lokalizace
extension EnhancedLocalizationExtension on String {
  /// Získání lokalizovaného textu
  String tr({Map<String, dynamic>? args}) {
    return EnhancedLocalizationService().getText(this, args: args);
  }
}

/// Pomocná třída pro lokalizační konstanty
class AppLocalizations {
  // Navigation
  static const String navHome = 'navHome';
  static const String navDiary = 'navDiary';
  static const String navLife = 'navLife';
  static const String navWallet = 'navWallet';
  static const String navCommunity = 'navCommunity';
  static const String navProfile = 'navProfile';

  // Common actions
  static const String actionSave = 'actionSave';
  static const String actionCancel = 'actionCancel';
  static const String actionDelete = 'actionDelete';
  static const String actionEdit = 'actionEdit';
  static const String actionShare = 'actionShare';

  // Error messages
  static const String errorNoInternet = 'errorNoInternet';
  static const String errorServerError = 'errorServerError';
  static const String errorInvalidData = 'errorInvalidData';

  // Success messages
  static const String successSaved = 'successSaved';
  static const String successDeleted = 'successDeleted';
  static const String successShared = 'successShared';
}
