import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/ai_orchestrator.dart';

/// Služba pro cloud AI (OpenAI, Google AI, atd.)
class CloudAIService {
  static final CloudAIService _instance = CloudAIService._internal();
  factory CloudAIService() => _instance;
  CloudAIService._internal();

  final Dio _dio = Dio();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  bool _isInitialized = false;
  String? _openAIKey;
  String? _googleAIKey;

  // Konfigurace
  static const String _openAIBaseUrl = 'https://api.openai.com/v1';
  static const String _googleAIBaseUrl =
      'https://generativelanguage.googleapis.com/v1beta';
  static const int _maxTokens = 1000;
  static const double _temperature = 0.7;

  /// Inicializace Cloud AI služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🌐 Inicializuji Cloud AI Service...');

      // Načtení API klíčů ze secure storage
      _openAIKey = await _secureStorage.read(key: 'openai_api_key');
      _googleAIKey = await _secureStorage.read(key: 'google_ai_key');

      // Konfigurace Dio
      _dio.options.connectTimeout = const Duration(seconds: 30);
      _dio.options.receiveTimeout = const Duration(seconds: 60);

      // Interceptor pro logování (pouze v debug módu)
      if (kDebugMode) {
        _dio.interceptors.add(
          LogInterceptor(
            requestBody: false, // Nelogujeme tělo kvůli API klíčům
            responseBody: true,
          ),
        );
      }

      _isInitialized = true;
      debugPrint('✅ Cloud AI Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Cloud AI: $e');
      _isInitialized = true; // Pokračujeme bez cloud AI
    }
  }

  /// Generování odpovědi pomocí cloud AI
  Future<AIResponse> generateResponse(String query, AIContext context) async {
    try {
      // Pokus o OpenAI
      if (_openAIKey != null) {
        final openAIResponse = await _generateOpenAIResponse(query, context);
        if (openAIResponse != null) return openAIResponse;
      }

      // Fallback na Google AI
      if (_googleAIKey != null) {
        final googleResponse = await _generateGoogleAIResponse(query, context);
        if (googleResponse != null) return googleResponse;
      }

      // Pokud nejsou dostupné API klíče
      return _createFallbackResponse();
    } catch (e) {
      debugPrint('❌ Chyba při generování cloud odpovědi: $e');
      return _createErrorResponse();
    }
  }

  /// Generování odpovědi pomocí OpenAI GPT
  Future<AIResponse?> _generateOpenAIResponse(
    String query,
    AIContext context,
  ) async {
    try {
      final systemPrompt = _buildSystemPrompt(context);
      final userPrompt = _buildUserPrompt(query, context);

      final response = await _dio.post(
        '$_openAIBaseUrl/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_openAIKey',
            'Content-Type': 'application/json',
          },
        ),
        data: {
          'model': 'gpt-3.5-turbo',
          'messages': [
            {'role': 'system', 'content': systemPrompt},
            {'role': 'user', 'content': userPrompt},
          ],
          'max_tokens': _maxTokens,
          'temperature': _temperature,
          'presence_penalty': 0.1,
          'frequency_penalty': 0.1,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final content = data['choices'][0]['message']['content'];
        final usage = data['usage'];

        return AIResponse(
          content: content,
          type: AIResponseType.text,
          confidence: 0.9,
          source: AIResponseSource.cloud,
          timestamp: DateTime.now(),
          metadata: {
            'provider': 'openai',
            'model': 'gpt-3.5-turbo',
            'tokens_used': usage['total_tokens'],
          },
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při OpenAI požadavku: $e');
    }
    return null;
  }

  /// Generování odpovědi pomocí Google AI
  Future<AIResponse?> _generateGoogleAIResponse(
    String query,
    AIContext context,
  ) async {
    try {
      final prompt = _buildGooglePrompt(query, context);

      final response = await _dio.post(
        '$_googleAIBaseUrl/models/gemini-pro:generateContent',
        options: Options(headers: {'Content-Type': 'application/json'}),
        queryParameters: {'key': _googleAIKey},
        data: {
          'contents': [
            {
              'parts': [
                {'text': prompt},
              ],
            },
          ],
          'generationConfig': {
            'temperature': _temperature,
            'topK': 40,
            'topP': 0.95,
            'maxOutputTokens': _maxTokens,
          },
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final content = data['candidates'][0]['content']['parts'][0]['text'];

        return AIResponse(
          content: content,
          type: AIResponseType.text,
          confidence: 0.85,
          source: AIResponseSource.cloud,
          timestamp: DateTime.now(),
          metadata: {'provider': 'google', 'model': 'gemini-pro'},
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při Google AI požadavku: $e');
    }
    return null;
  }

  /// Překlad textu
  Future<String?> translateText(String text, String targetLanguage) async {
    try {
      if (_googleAIKey == null) return null;

      final response = await _dio.post(
        'https://translation.googleapis.com/language/translate/v2',
        options: Options(headers: {'Content-Type': 'application/json'}),
        queryParameters: {'key': _googleAIKey},
        data: {'q': text, 'target': targetLanguage, 'format': 'text'},
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return data['data']['translations'][0]['translatedText'];
      }
    } catch (e) {
      debugPrint('❌ Chyba při překladu: $e');
    }
    return null;
  }

  /// Analýza sentimentu
  Future<SentimentAnalysis?> analyzeSentiment(String text) async {
    try {
      if (_googleAIKey == null) return null;

      final response = await _dio.post(
        'https://language.googleapis.com/v1/documents:analyzeSentiment',
        options: Options(headers: {'Content-Type': 'application/json'}),
        queryParameters: {'key': _googleAIKey},
        data: {
          'document': {'type': 'PLAIN_TEXT', 'content': text},
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final sentiment = data['documentSentiment'];

        return SentimentAnalysis(
          score: sentiment['score'].toDouble(),
          magnitude: sentiment['magnitude'].toDouble(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při analýze sentimentu: $e');
    }
    return null;
  }

  /// Sestavení system promptu pro OpenAI
  String _buildSystemPrompt(AIContext context) {
    final buffer = StringBuffer();

    buffer.writeln('Jsi inteligentní cestovní asistent pro Chorvatsko.');
    buffer.writeln(
      'Tvým úkolem je pomáhat turistům s plánováním cest, doporučeními a informacemi.',
    );
    buffer.writeln('Odpovídej v češtině, pokud není požadován jiný jazyk.');
    buffer.writeln('Buď přátelský, užitečný a poskytuj konkrétní informace.');

    if (context.currentLocation != null) {
      buffer.writeln(
        'Uživatel se nachází v: ${context.currentLocation!.city ?? 'neznámé lokaci'}',
      );
    }

    if (context.userProfile != null) {
      buffer.writeln(
        'Uživatelské preference: ${context.userProfile!.interests.join(', ')}',
      );
      buffer.writeln(
        'Styl cestování: ${context.userProfile!.travelStyle.name}',
      );
      buffer.writeln('Rozpočet: ${context.userProfile!.budget.name}');
    }

    if (context.recentQueries.isNotEmpty) {
      buffer.writeln(
        'Nedávné dotazy: ${context.recentQueries.take(3).join(', ')}',
      );
    }

    return buffer.toString();
  }

  /// Sestavení user promptu
  String _buildUserPrompt(String query, AIContext context) {
    return query;
  }

  /// Sestavení promptu pro Google AI
  String _buildGooglePrompt(String query, AIContext context) {
    final systemPrompt = _buildSystemPrompt(context);
    return '$systemPrompt\n\nUživatelský dotaz: $query';
  }

  /// Vytvoření fallback odpovědi
  AIResponse _createFallbackResponse() {
    return AIResponse(
      content:
          'Cloud AI služby nejsou momentálně dostupné. Zkusím odpovědět pomocí lokálních znalostí.',
      type: AIResponseType.text,
      confidence: 0.3,
      source: AIResponseSource.fallback,
      timestamp: DateTime.now(),
    );
  }

  /// Vytvoření chybové odpovědi
  AIResponse _createErrorResponse() {
    return AIResponse(
      content: 'Omlouvám se, došlo k chybě při komunikaci s cloud AI službami.',
      type: AIResponseType.error,
      confidence: 0.0,
      source: AIResponseSource.cloud,
      timestamp: DateTime.now(),
    );
  }

  /// Nastavení API klíčů
  Future<void> setOpenAIKey(String key) async {
    await _secureStorage.write(key: 'openai_api_key', value: key);
    _openAIKey = key;
  }

  Future<void> setGoogleAIKey(String key) async {
    await _secureStorage.write(key: 'google_ai_key', value: key);
    _googleAIKey = key;
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  bool get hasOpenAI => _openAIKey != null;
  bool get hasGoogleAI => _googleAIKey != null;
}

/// Analýza sentimentu
class SentimentAnalysis {
  final double score; // -1.0 (negativní) až 1.0 (pozitivní)
  final double magnitude; // 0.0 až nekonečno (síla emocí)

  const SentimentAnalysis({required this.score, required this.magnitude});

  bool get isPositive => score > 0.1;
  bool get isNegative => score < -0.1;
  bool get isNeutral => score >= -0.1 && score <= 0.1;
  bool get isStrong => magnitude > 0.6;
}
