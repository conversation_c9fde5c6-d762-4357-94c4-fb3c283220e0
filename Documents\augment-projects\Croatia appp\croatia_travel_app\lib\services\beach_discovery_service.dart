import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/beach.dart';

/// Služba pro objevování pláží a vodních aktivit v Chorvatsku
/// Právně bezpečné řešení bez API závislostí
class BeachDiscoveryService {
  static final BeachDiscoveryService _instance =
      BeachDiscoveryService._internal();
  factory BeachDiscoveryService() => _instance;
  BeachDiscoveryService._internal();

  // Cache pro pláže
  List<Beach>? _cachedBeaches;
  List<WaterSport>? _cachedWaterSports;
  DateTime? _lastCacheUpdate;
  final Duration _cacheValidDuration = const Duration(hours: 3);

  /// Získá všechny pláže
  Future<List<Beach>> getAllBeaches() async {
    if (_isCacheValid() && _cachedBeaches != null) {
      return _cachedBeaches!;
    }

    try {
      // Simulace načítání z lokální databáze
      await Future.delayed(const Duration(milliseconds: 600));

      _cachedBeaches = _generateSampleBeaches();
      _lastCacheUpdate = DateTime.now();

      return _cachedBeaches!;
    } catch (e) {
      debugPrint('Chyba při načítání pláží: $e');
      return [];
    }
  }

  /// Získá všechny vodní sporty
  Future<List<WaterSport>> getAllWaterSports() async {
    if (_isCacheValid() && _cachedWaterSports != null) {
      return _cachedWaterSports!;
    }

    try {
      await Future.delayed(const Duration(milliseconds: 400));

      _cachedWaterSports = _generateSampleWaterSports();
      _lastCacheUpdate = DateTime.now();

      return _cachedWaterSports!;
    } catch (e) {
      debugPrint('Chyba při načítání vodních sportů: $e');
      return [];
    }
  }

  /// Vyhledá pláže podle kritérií
  Future<List<Beach>> searchBeaches({
    String? query,
    String? region,
    BeachType? beachType,
    String? accessType,
    bool? hasLifeguard,
    bool? hasParking,
    bool? hasRestaurant,
    bool? isPetFriendly,
    bool? isAccessible,
    double? latitude,
    double? longitude,
    double? maxDistance,
    double? minRating,
  }) async {
    final allBeaches = await getAllBeaches();

    return allBeaches.where((beach) {
      // Text search
      if (query != null && query.isNotEmpty) {
        final searchLower = query.toLowerCase();
        if (!beach.name.toLowerCase().contains(searchLower) &&
            !beach.description.toLowerCase().contains(searchLower) &&
            !beach.location.toLowerCase().contains(searchLower)) {
          return false;
        }
      }

      // Region filter
      if (region != null && region != 'all' && beach.region != region) {
        return false;
      }

      // Beach type filter
      if (beachType != null && beach.beachType != beachType) {
        return false;
      }

      // Access type filter
      if (accessType != null &&
          accessType != 'all' &&
          beach.accessType != accessType) {
        return false;
      }

      // Feature filters
      if (hasLifeguard == true && !beach.hasLifeguard) return false;
      if (hasParking == true && !beach.hasParking) return false;
      if (hasRestaurant == true && !beach.hasRestaurant) return false;
      if (isPetFriendly == true && !beach.isPetFriendly) return false;
      if (isAccessible == true && !beach.isAccessible) return false;

      // Distance filter
      if (latitude != null && longitude != null && maxDistance != null) {
        final distance = beach.distanceFrom(latitude, longitude);
        if (distance > maxDistance) {
          return false;
        }
      }

      // Rating filter
      if (minRating != null && beach.rating < minRating) {
        return false;
      }

      return true;
    }).toList();
  }

  /// Získá top hodnocené pláže
  Future<List<Beach>> getTopRatedBeaches({int limit = 20}) async {
    final allBeaches = await getAllBeaches();

    final topRated = allBeaches.where((b) => b.rating >= 4.5).toList();
    topRated.sort((a, b) => b.rating.compareTo(a.rating));

    return topRated.take(limit).toList();
  }

  /// Získá rodinné pláže
  Future<List<Beach>> getFamilyFriendlyBeaches({int limit = 20}) async {
    final allBeaches = await getAllBeaches();

    final familyFriendly = allBeaches.where((b) => b.isFamilyFriendly).toList();
    familyFriendly.sort((a, b) => b.rating.compareTo(a.rating));

    return familyFriendly.take(limit).toList();
  }

  /// Získá pláže pro vodní sporty
  Future<List<Beach>> getWaterSportsBeaches({int limit = 20}) async {
    final allBeaches = await getAllBeaches();

    final waterSports = allBeaches
        .where(
          (b) => b.activities.any(
            (activity) => [
              'windsurfing',
              'kitesurfing',
              'diving',
              'sailing',
            ].contains(activity),
          ),
        )
        .toList();
    waterSports.sort((a, b) => b.rating.compareTo(a.rating));

    return waterSports.take(limit).toList();
  }

  /// Získá pláže v blízkosti
  Future<List<Beach>> getNearbyBeaches(
    double latitude,
    double longitude, {
    double maxDistance = 20.0, // km
    int limit = 20,
  }) async {
    final allBeaches = await getAllBeaches();

    final nearby = allBeaches
        .map((beach) {
          final distance = beach.distanceFrom(latitude, longitude);
          return MapEntry(beach, distance);
        })
        .where((entry) => entry.value <= maxDistance)
        .toList();

    nearby.sort((a, b) => a.value.compareTo(b.value));

    return nearby.take(limit).map((entry) => entry.key).toList();
  }

  /// Získá aktuální podmínky na pláži
  Future<BeachConditions?> getBeachConditions(String beachId) async {
    try {
      // Simulace načtení aktuálních podmínek
      await Future.delayed(const Duration(milliseconds: 300));

      final random = Random();
      return BeachConditions(
        beachId: beachId,
        seaTemperature: 18.0 + random.nextDouble() * 8.0, // 18-26°C
        airTemperature: 20.0 + random.nextDouble() * 12.0, // 20-32°C
        waveHeight: random.nextInt(150), // 0-150cm
        windSpeed: random.nextInt(40), // 0-40 km/h
        windDirection: [
          'N',
          'NE',
          'E',
          'SE',
          'S',
          'SW',
          'W',
          'NW',
        ][random.nextInt(8)],
        uvIndex: 3 + random.nextInt(8), // 3-10
        weatherCondition: [
          'sunny',
          'partly_cloudy',
          'cloudy',
          'light_rain',
        ][random.nextInt(4)],
        visibility: 5 + random.nextInt(15), // 5-20 km
        timestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Chyba při načítání podmínek: $e');
      return null;
    }
  }

  /// Získá statistiky pláží
  Future<BeachStatistics> getBeachStatistics() async {
    final allBeaches = await getAllBeaches();
    final allWaterSports = await getAllWaterSports();

    return BeachStatistics(
      totalBeaches: allBeaches.length,
      topRated: allBeaches.where((b) => b.rating >= 4.5).length,
      familyFriendly: allBeaches.where((b) => b.isFamilyFriendly).length,
      waterSports: allBeaches.where((b) => b.activities.isNotEmpty).length,
      byRegion: _getBeachesByRegion(allBeaches),
      byType: _getBeachesByType(allBeaches),
      totalWaterSports: allWaterSports.length,
    );
  }

  /// Kontroluje platnost cache
  bool _isCacheValid() {
    return _lastCacheUpdate != null &&
        DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration;
  }

  /// Generuje ukázková data pláží
  List<Beach> _generateSampleBeaches() {
    final beaches = <Beach>[];

    // Istrian beaches
    beaches.addAll([
      _createBeach(
        'Zlatni Rat',
        'Nejslavnější pláž v Chorvatsku s unikátním tvarem měnícím se podle větru',
        'Bol, ostrov Brač',
        43.2567,
        16.6378,
        'dalmatia',
        BeachType.pebble,
        ['windsurfing', 'kitesurfing', 'swimming', 'sailing'],
        4.8,
        342,
        hasLifeguard: true,
        hasParking: true,
        hasRestaurant: true,
      ),
      _createBeach(
        'Sakarun',
        'Písečná pláž s tyrkysovou vodou na ostrově Dugi Otok',
        'Dugi Otok',
        44.1234,
        15.1567,
        'dalmatia',
        BeachType.sandy,
        ['swimming', 'snorkeling', 'kayaking'],
        4.7,
        198,
        hasLifeguard: true,
        hasParking: true,
      ),
    ]);

    // Dalmatian beaches
    beaches.addAll([
      _createBeach(
        'Bačvice',
        'Městská písečná pláž ve Splitu, ideální pro rodiny',
        'Split',
        43.5000,
        16.4500,
        'dalmatia',
        BeachType.sandy,
        ['swimming', 'volleyball', 'picigin'],
        4.3,
        567,
        hasLifeguard: true,
        hasParking: true,
        hasRestaurant: true,
        hasShower: true,
      ),
      _createBeach(
        'Stiniva',
        'Skrytá pláž na ostrově Vis s úzkým vstupem',
        'Vis',
        43.0123,
        16.1234,
        'dalmatia',
        BeachType.pebble,
        ['swimming', 'snorkeling'],
        4.9,
        89,
        hasParking: false,
        hasRestaurant: false,
      ),
    ]);

    // Istrian beaches
    beaches.addAll([
      _createBeach(
        'Kamenjak',
        'Přírodní park s divokými plážemi a čistým mořem',
        'Premantura, Istrie',
        44.7890,
        13.9123,
        'istria',
        BeachType.rocky,
        ['diving', 'snorkeling', 'fishing', 'kayaking'],
        4.6,
        234,
        hasLifeguard: false,
        hasParking: true,
        hasRestaurant: true,
      ),
      _createBeach(
        'Lone Bay',
        'Oblázková pláž obklopená borovicovým lesem',
        'Rovinj, Istrie',
        45.0678,
        13.6345,
        'istria',
        BeachType.pebble,
        ['swimming', 'windsurfing', 'sailing'],
        4.4,
        156,
        hasLifeguard: true,
        hasParking: true,
        hasRestaurant: true,
      ),
    ]);

    // Kvarner beaches
    beaches.addAll([
      _createBeach(
        'Rajska plaža',
        'Písečná pláž na ostrově Rab s mělkou vodou',
        'Lopar, Rab',
        44.8234,
        14.7345,
        'kvarner',
        BeachType.sandy,
        ['swimming', 'volleyball', 'kayaking'],
        4.5,
        278,
        hasLifeguard: true,
        hasParking: true,
        hasRestaurant: true,
        isPetFriendly: true,
      ),
    ]);

    // Přidá náhodné pláže
    for (int i = 0; i < 10; i++) {
      beaches.add(_createRandomBeach(Random()));
    }

    return beaches;
  }

  Beach _createBeach(
    String name,
    String description,
    String location,
    double latitude,
    double longitude,
    String region,
    BeachType beachType,
    List<String> activities,
    double rating,
    int reviewCount, {
    bool hasLifeguard = false,
    bool hasParking = false,
    bool hasRestaurant = false,
    bool hasShower = false,
    bool hasToilet = false,
    bool isPetFriendly = false,
    bool isAccessible = false,
  }) {
    return Beach(
      id: 'beach_${name.toLowerCase().replaceAll(' ', '_')}',
      name: name,
      description: description,
      location: location,
      latitude: latitude,
      longitude: longitude,
      region: region,
      beachType: beachType,
      features: _generateBeachFeatures(),
      activities: activities,
      rating: rating,
      reviewCount: reviewCount,
      accessType: 'free',
      hasLifeguard: hasLifeguard,
      hasParking: hasParking,
      hasRestaurant: hasRestaurant,
      hasShower: hasShower,
      hasToilet: hasToilet,
      hasUmbrella: hasRestaurant,
      hasChair: hasRestaurant,
      isPetFriendly: isPetFriendly,
      isNudist: false,
      isAccessible: isAccessible,
      photos: [],
      lastUpdated: DateTime.now(),
    );
  }

  Beach _createRandomBeach(Random random) {
    final regions = ['dalmatia', 'istria', 'kvarner', 'dubrovnik'];
    final beachTypes = [BeachType.sandy, BeachType.pebble, BeachType.rocky];
    final activities = ['swimming', 'snorkeling', 'windsurfing', 'kayaking'];

    final names = [
      'Pláž Sunset',
      'Pláž Paradise',
      'Pláž Crystal',
      'Pláž Golden',
      'Pláž Azure',
      'Pláž Emerald',
      'Pláž Coral',
      'Pláž Pearl',
    ];

    final region = regions[random.nextInt(regions.length)];
    final name =
        '${names[random.nextInt(names.length)]} ${random.nextInt(100)}';

    return Beach(
      id: 'beach_random_${random.nextInt(10000)}',
      name: name,
      description: 'Krásná pláž s čistým mořem a příjemnou atmosférou',
      location: 'Náhodná lokace, Chorvatsko',
      latitude: 42.0 + random.nextDouble() * 4.0,
      longitude: 13.0 + random.nextDouble() * 6.0,
      region: region,
      beachType: beachTypes[random.nextInt(beachTypes.length)],
      features: _generateBeachFeatures(),
      activities: activities.take(1 + random.nextInt(3)).toList(),
      rating: 3.5 + random.nextDouble() * 1.5,
      reviewCount: 20 + random.nextInt(200),
      accessType: 'free',
      hasLifeguard: random.nextBool(),
      hasParking: random.nextBool(),
      hasRestaurant: random.nextBool(),
      hasShower: random.nextBool(),
      hasToilet: random.nextBool(),
      hasUmbrella: random.nextBool(),
      hasChair: random.nextBool(),
      isPetFriendly: random.nextBool(),
      isNudist: false,
      isAccessible: random.nextBool(),
      photos: [],
      lastUpdated: DateTime.now(),
    );
  }

  List<String> _generateBeachFeatures() {
    return ['clean_water', 'scenic_view', 'easy_access'];
  }

  List<WaterSport> _generateSampleWaterSports() {
    return [
      WaterSport(
        id: 'windsurfing',
        name: 'Windsurfing',
        description: 'Kombinace surfování a plachtění s využitím větru',
        type: WaterActivity.windsurfing,
        difficulty: 'intermediate',
        equipment: ['prkno', 'plachta', 'neopren'],
        suitableBeaches: ['zlatni_rat', 'lone_bay'],
        season: 'duben-říjen',
        averagePrice: 150,
        priceUnit: 'za den',
        requiresLicense: false,
        minAge: 12,
        providers: ['Windsurfing Split', 'Bol Windsurfing'],
      ),
      WaterSport(
        id: 'diving',
        name: 'Potápění',
        description: 'Objevování podmořského světa Jadranu',
        type: WaterActivity.diving,
        difficulty: 'beginner',
        equipment: ['maska', 'šnorchl', 'ploutve', 'neopren'],
        suitableBeaches: ['kamenjak', 'stiniva'],
        season: 'celoročně',
        averagePrice: 200,
        priceUnit: 'za ponor',
        requiresLicense: true,
        minAge: 10,
        providers: ['Diving Center Split', 'Istria Diving'],
      ),
    ];
  }

  Map<String, int> _getBeachesByRegion(List<Beach> beaches) {
    final Map<String, int> result = {};
    for (final beach in beaches) {
      result[beach.region] = (result[beach.region] ?? 0) + 1;
    }
    return result;
  }

  Map<String, int> _getBeachesByType(List<Beach> beaches) {
    final Map<String, int> result = {};
    for (final beach in beaches) {
      final type = beach.beachType.toString().split('.').last;
      result[type] = (result[type] ?? 0) + 1;
    }
    return result;
  }
}

/// Statistiky pláží
class BeachStatistics {
  final int totalBeaches;
  final int topRated;
  final int familyFriendly;
  final int waterSports;
  final Map<String, int> byRegion;
  final Map<String, int> byType;
  final int totalWaterSports;

  BeachStatistics({
    required this.totalBeaches,
    required this.topRated,
    required this.familyFriendly,
    required this.waterSports,
    required this.byRegion,
    required this.byType,
    required this.totalWaterSports,
  });
}
