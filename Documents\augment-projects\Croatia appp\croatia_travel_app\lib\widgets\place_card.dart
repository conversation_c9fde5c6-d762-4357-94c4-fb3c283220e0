import 'package:flutter/material.dart';
import '../theme/watercolor_theme.dart';
import '../models/place.dart';
import '../widgets/accessible_widget.dart';
import '../services/accessibility_service.dart';

class PlaceCard extends StatelessWidget {
  final Place place;
  final VoidCallback? onTap;
  final VoidCallback? onVisitedToggle;
  final bool showDistance;
  final double? distance;

  const PlaceCard({
    super.key,
    required this.place,
    this.onTap,
    this.onVisitedToggle,
    this.showDistance = false,
    this.distance,
  });

  @override
  Widget build(BuildContext context) {
    final accessibilityService = AccessibilityService();

    // Vytvoření semantic label pro screen readery
    final semanticLabel = accessibilityService.getAccessibilityLabel(
      '${place.name}. ${place.shortDescription}. ${_getTypeLabel(place.type)}',
      context: 'Místo k návštěvě',
    );

    return AccessibleCard(
      onTap: onTap,
      semanticLabel: semanticLabel,
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Obrázek nahoře - podle vzoru z telefonu
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: _getGradientColors(place.type),
              ),
            ),
            child: ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              child: Stack(
                children: [
                  // Placeholder obrázek s ikonou
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.9),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            _getPlaceIcon(place.type),
                            size: 40,
                            color: _getTypeColor(place.type),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _getTypeLabel(place.type),
                          style: WatercolorTheme.labelMedium.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Označení navštíveno
                  if (place.isVisited)
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: const BoxDecoration(
                          color: Color(0xFF4CAF50),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),

                  // Hodnocení
                  if (place.rating != null)
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.star,
                              size: 12,
                              color: Colors.white,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              place.rating!.toStringAsFixed(1),
                              style: WatercolorTheme.labelMedium.copyWith(
                                fontSize: 11,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Text pod obrázkem - podle vzoru z telefonu
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Název místa - hlavní nadpis
                Text(
                  place.name,
                  style: WatercolorTheme.headingSmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 8),

                // Popis místa - zkrácený
                Text(
                  place.shortDescription,
                  style: WatercolorTheme.bodyMedium,
                  maxLines: 2, // Zkráceno z 3 na 2 řádky
                  overflow: TextOverflow.ellipsis,
                ),

                // "Číst více" tlačítko pokud je popis delší
                if (place.description.length > 120) ...[
                  const SizedBox(height: 8),
                  GestureDetector(
                    onTap: onTap, // Otevře detail místa
                    child: Text(
                      'Saznaj više',
                      style: WatercolorTheme.labelMedium.copyWith(
                        fontSize: 13,
                        color: WatercolorTheme.adriaticBlue,
                      ),
                    ),
                  ),
                ],

                const SizedBox(height: 12),

                // Region a typ místa
                Row(
                  children: [
                    // Region tag
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(
                          0xFF2E8B8B,
                        ).withValues(alpha: 0.1), // Středomořská tyrkysová
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xFF2E8B8B).withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        _getRegionLabel(place.region),
                        style: WatercolorTheme.labelMedium.copyWith(
                          fontSize: 11,
                          color: WatercolorTheme.mediterraneanTeal,
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Typ místa tag
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getTypeColor(place.type).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _getTypeColor(
                            place.type,
                          ).withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        _getTypeLabel(place.type),
                        style: WatercolorTheme.labelMedium.copyWith(
                          fontSize: 11,
                          color: _getTypeColor(place.type),
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Bookmark tlačítko
                    if (onVisitedToggle != null)
                      InkWell(
                        onTap: onVisitedToggle,
                        borderRadius: BorderRadius.circular(20),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            place.isVisited
                                ? Icons.bookmark
                                : Icons.bookmark_border,
                            size: 24,
                            color: place.isVisited
                                ? const Color(0xFF4CAF50)
                                : const Color(0xFF666666),
                          ),
                        ),
                      ),
                  ],
                ),

                // Vzdálenost (pokud je k dispozici)
                if (showDistance && distance != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: const Color(0xFF666666),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatDistance(distance!),
                        style: WatercolorTheme.bodySmall.copyWith(
                          color: const Color(0xFF666666),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],

                // Datum návštěvy
                if (place.isVisited && place.visitedDate != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        size: 16,
                        color: const Color(0xFF4CAF50),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Posjećeno ${_formatDate(place.visitedDate!)}',
                        style: WatercolorTheme.bodySmall.copyWith(
                          color: const Color(0xFF4CAF50),
                          fontWeight: FontWeight.w500,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Color> _getGradientColors(PlaceType type) {
    switch (type) {
      case PlaceType.monument:
        return [Colors.brown.shade400, Colors.brown.shade600];
      case PlaceType.beach:
        return [Colors.blue.shade400, Colors.blue.shade600];
      case PlaceType.restaurant:
        return [Colors.orange.shade400, Colors.orange.shade600];
      case PlaceType.hotel:
        return [Colors.purple.shade400, Colors.purple.shade600];
      case PlaceType.museum:
        return [Colors.indigo.shade400, Colors.indigo.shade600];
      case PlaceType.park:
        return [Colors.green.shade400, Colors.green.shade600];
      case PlaceType.church:
        return [Colors.grey.shade400, Colors.grey.shade600];
      case PlaceType.castle:
        return [Colors.red.shade400, Colors.red.shade600];
      case PlaceType.viewpoint:
        return [Colors.teal.shade400, Colors.teal.shade600];
      case PlaceType.other:
        return [Colors.blueGrey.shade400, Colors.blueGrey.shade600];
    }
  }

  IconData _getPlaceIcon(PlaceType type) {
    switch (type) {
      case PlaceType.monument:
        return Icons.account_balance;
      case PlaceType.beach:
        return Icons.beach_access;
      case PlaceType.restaurant:
        return Icons.restaurant;
      case PlaceType.hotel:
        return Icons.hotel;
      case PlaceType.museum:
        return Icons.museum;
      case PlaceType.park:
        return Icons.park;
      case PlaceType.church:
        return Icons.church;
      case PlaceType.castle:
        return Icons.castle;
      case PlaceType.viewpoint:
        return Icons.landscape;
      case PlaceType.other:
        return Icons.place;
    }
  }

  Color _getTypeColor(PlaceType type) {
    switch (type) {
      case PlaceType.monument:
        return Colors.brown;
      case PlaceType.beach:
        return Colors.blue;
      case PlaceType.restaurant:
        return Colors.orange;
      case PlaceType.hotel:
        return Colors.purple;
      case PlaceType.museum:
        return Colors.indigo;
      case PlaceType.park:
        return Colors.green;
      case PlaceType.church:
        return Colors.grey;
      case PlaceType.castle:
        return Colors.red;
      case PlaceType.viewpoint:
        return Colors.teal;
      case PlaceType.other:
        return Colors.blueGrey;
    }
  }

  String _getTypeLabel(PlaceType type) {
    switch (type) {
      case PlaceType.monument:
        return 'Památka';
      case PlaceType.beach:
        return 'Pláž';
      case PlaceType.restaurant:
        return 'Restaurace';
      case PlaceType.hotel:
        return 'Hotel';
      case PlaceType.museum:
        return 'Muzeum';
      case PlaceType.park:
        return 'Park';
      case PlaceType.church:
        return 'Kostel';
      case PlaceType.castle:
        return 'Hrad';
      case PlaceType.viewpoint:
        return 'Vyhlídka';
      case PlaceType.other:
        return 'Ostatní';
    }
  }

  String _getRegionLabel(String region) {
    switch (region) {
      case 'istria':
        return 'Istrie';
      case 'dalmatia':
        return 'Dalmácie';
      case 'slavonia':
        return 'Slavonie';
      case 'lika':
        return 'Lika';
      case 'zagreb':
        return 'Zagreb';
      default:
        return region;
    }
  }

  String _formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()} m';
    } else {
      return '${(distanceInMeters / 1000).toStringAsFixed(1)} km';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year}';
  }
}
