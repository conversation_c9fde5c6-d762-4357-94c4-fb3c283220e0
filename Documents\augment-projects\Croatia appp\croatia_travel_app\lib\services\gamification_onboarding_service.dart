import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/gamification_onboarding.dart';
// import '../services/achievement_system_service.dart';

/// 🎮 GAMIFICATION ONBOARDING SERVICE - Maximize retention impact
class GamificationOnboardingService {
  static final GamificationOnboardingService _instance =
      GamificationOnboardingService._internal();
  factory GamificationOnboardingService() => _instance;
  GamificationOnboardingService._internal();

  bool _isInitialized = false;
  final List<OnboardingQuest> _quests = [];
  final List<OnboardingMilestone> _milestones = [];
  final Map<String, UserOnboardingProgress> _userProgress = {};
  final StreamController<OnboardingEvent> _eventController =
      StreamController.broadcast();

  /// Stream onboarding událostí
  Stream<OnboardingEvent> get onboardingEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🎮 Inicializuji Gamification Onboarding Service...');

      await _loadOnboardingQuests();
      await _loadOnboardingMilestones();
      await _loadUserProgress();

      _isInitialized = true;
      debugPrint('✅ Gamification Onboarding Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Gamification Onboarding: $e');
      await _createDefaultQuests();
      _isInitialized = true;
    }
  }

  /// Spuštění gamifikovaného onboardingu
  Future<OnboardingJourney> startGamifiedOnboarding({
    required String userId,
    OnboardingPersonality? personality,
  }) async {
    try {
      // Analýza personality pro personalizaci
      final detectedPersonality =
          personality ?? await _detectOnboardingPersonality(userId);

      // Výběr questů podle personality
      final selectedQuests = _selectQuestsForPersonality(detectedPersonality);

      // Vytvoření onboarding journey
      final journey = OnboardingJourney(
        id: 'journey_${DateTime.now().millisecondsSinceEpoch}',
        title: 'Onboarding Journey',
        description: 'Personalizovaná cesta onboardingu',
        quests: selectedQuests,
        milestones: _milestones,
        estimatedDays: 7,
        targetPersonality: detectedPersonality,
        configuration: {
          'userId': userId,
          'startedAt': DateTime.now().toIso8601String(),
        },
      );

      // Uložení pokroku
      _userProgress[userId] = UserOnboardingProgress(
        userId: userId,
        journeyId: journey.id,
        completedQuests: [],
        completedMilestones: [],
        totalPoints: 0,
        currentStreak: 0,
        startedAt: DateTime.now(),
        personality: detectedPersonality,
        preferences: {},
        categoryProgress: {},
      );

      await _saveUserProgress();

      // První quest
      await _startQuest(userId, selectedQuests.first);

      _eventController.add(
        OnboardingEvent(
          id: 'event_${DateTime.now().millisecondsSinceEpoch}',
          type: OnboardingEventType.questStarted,
          userId: userId,
          timestamp: DateTime.now(),
          data: {
            'personality': detectedPersonality.name,
            'questsCount': selectedQuests.length,
          },
        ),
      );

      debugPrint(
        '🎮 Gamifikovaný onboarding spuštěn: $userId (${detectedPersonality.name})',
      );
      return journey;
    } catch (e) {
      debugPrint('❌ Chyba při spuštění onboardingu: $e');
      rethrow;
    }
  }

  /// Dokončení questu
  Future<QuestCompletionResult> completeQuest({
    required String userId,
    required String questId,
    Map<String, dynamic>? completionData,
  }) async {
    try {
      final progress = _userProgress[userId];
      if (progress == null) {
        throw Exception('Uživatel nemá aktivní onboarding');
      }

      final quest = _quests.firstWhere((q) => q.id == questId);

      // Výpočet XP a odměn
      final baseXP = quest.rewards['xp'] as int? ?? 100;
      final bonusXP = _calculateBonusXP(quest, completionData);
      final totalXP = baseXP + bonusXP;

      // Aktualizace pokroku
      final newTotalPoints = progress.totalPoints + totalXP;
      final updatedProgress = UserOnboardingProgress(
        userId: progress.userId,
        journeyId: progress.journeyId,
        completedQuests: [...progress.completedQuests, questId],
        completedMilestones: progress.completedMilestones,
        totalPoints: newTotalPoints,
        currentStreak: progress.currentStreak + 1,
        startedAt: progress.startedAt,
        completedAt: progress.completedAt,
        personality: progress.personality,
        preferences: progress.preferences,
        categoryProgress: progress.categoryProgress,
      );

      _userProgress[userId] = updatedProgress;
      await _saveUserProgress();

      // Kontrola milníků
      final unlockedMilestones = await _checkMilestones(
        userId,
        updatedProgress,
      );

      // Kontrola achievementů
      // TODO: Implementovat achievement system
      debugPrint('🏆 Achievement check: quest completed $questId');

      // Další quest
      OnboardingQuest? nextQuest;
      final journey = await _getOnboardingJourney(userId);
      if (journey != null) {
        final currentIndex = journey.quests.indexWhere((q) => q.id == questId);
        if (currentIndex >= 0 && currentIndex < journey.quests.length - 1) {
          nextQuest = journey.quests[currentIndex + 1];
          await _startQuest(userId, nextQuest);
        } else {
          // Onboarding dokončen
          await _completeOnboarding(userId);
        }
      }

      final result = QuestCompletionResult(
        questId: questId,
        userId: userId,
        completedAt: DateTime.now(),
        pointsEarned: totalXP,
        unlockedFeatures: ['Basic features'],
        earnedBadges: [],
        triggeredMilestone: unlockedMilestones.isNotEmpty,
        nextRecommendedQuest: nextQuest?.id,
      );

      _eventController.add(
        OnboardingEvent(
          id: 'event_${DateTime.now().millisecondsSinceEpoch}',
          type: OnboardingEventType.questCompleted,
          userId: userId,
          questId: questId,
          timestamp: DateTime.now(),
          data: {
            'questId': questId,
            'xpEarned': totalXP,
            'newLevel': _calculateLevel(updatedProgress.totalPoints),
          },
        ),
      );

      debugPrint('✅ Quest dokončen: $questId (+$totalXP XP)');
      return result;
    } catch (e) {
      debugPrint('❌ Chyba při dokončování questu: $e');
      rethrow;
    }
  }

  /// Spuštění konkrétního questu
  Future<void> _startQuest(String userId, OnboardingQuest quest) async {
    try {
      // Aktualizace current quest
      final progress = _userProgress[userId];
      if (progress != null) {
        _userProgress[userId] = UserOnboardingProgress(
          userId: progress.userId,
          journeyId: progress.journeyId,
          completedQuests: progress.completedQuests,
          completedMilestones: progress.completedMilestones,
          totalPoints: progress.totalPoints,
          currentStreak: progress.currentStreak,
          startedAt: progress.startedAt,
          completedAt: progress.completedAt,
          personality: progress.personality,
          preferences: {...progress.preferences, 'currentQuest': quest.id},
          categoryProgress: progress.categoryProgress,
        );
        await _saveUserProgress();
      }

      _eventController.add(
        OnboardingEvent(
          id: 'event_${DateTime.now().millisecondsSinceEpoch}',
          type: OnboardingEventType.questStarted,
          userId: userId,
          questId: quest.id,
          timestamp: DateTime.now(),
          data: {
            'questId': quest.id,
            'questType': quest.category,
            'difficulty': quest.difficulty.toString(),
          },
        ),
      );

      debugPrint('🎯 Quest spuštěn: ${quest.title}');
    } catch (e) {
      debugPrint('❌ Chyba při spuštění questu: $e');
    }
  }

  /// Dokončení celého onboardingu
  Future<OnboardingCompletionReward> _completeOnboarding(String userId) async {
    try {
      final progress = _userProgress[userId];
      if (progress == null) {
        return OnboardingCompletionReward(
          id: 'empty_reward',
          title: 'No Reward',
          description: 'No progress found',
          type: 'none',
          value: {},
          isRedeemed: false,
        );
      }

      // Completion bonus
      const completionBonus = 500;
      final finalXP = progress.totalPoints + completionBonus;
      final finalLevel = _calculateLevel(finalXP);

      // Speciální achievement
      debugPrint('🏆 Onboarding completed achievement unlocked');

      // Completion reward
      final reward = OnboardingCompletionReward(
        id: 'onboarding_completion_${DateTime.now().millisecondsSinceEpoch}',
        title: 'Onboarding Master',
        description:
            'Congratulations! You have completed the onboarding journey.',
        type: 'completion',
        value: {
          'totalXP': finalXP,
          'finalLevel': finalLevel,
          'completionBonus': completionBonus,
          'unlockedFeatures': [
            'Advanced Search Preview',
            'Analytics Dashboard Preview',
            'Premium Trial Access',
          ],
          'premiumTrialDays': 7,
        },
        expiresAt: DateTime.now().add(const Duration(days: 30)),
        isRedeemed: false,
      );

      // Aktualizace pokroku
      _userProgress[userId] = UserOnboardingProgress(
        userId: progress.userId,
        journeyId: progress.journeyId,
        completedQuests: progress.completedQuests,
        completedMilestones: progress.completedMilestones,
        totalPoints: finalXP,
        currentStreak: progress.currentStreak,
        startedAt: progress.startedAt,
        completedAt: DateTime.now(),
        personality: progress.personality,
        preferences: progress.preferences,
        categoryProgress: progress.categoryProgress,
      );
      await _saveUserProgress();

      _eventController.add(
        OnboardingEvent(
          id: 'event_${DateTime.now().millisecondsSinceEpoch}',
          type: OnboardingEventType.questCompleted, // Použijeme existující typ
          userId: userId,
          timestamp: DateTime.now(),
          data: {
            'finalXP': finalXP,
            'finalLevel': finalLevel,
            'completionBonus': completionBonus,
            'onboardingCompleted': true,
          },
        ),
      );

      debugPrint('🎉 Onboarding dokončen: $userId (Level $finalLevel)');
      return reward;
    } catch (e) {
      debugPrint('❌ Chyba při dokončování onboardingu: $e');
      return OnboardingCompletionReward(
        id: 'error_reward',
        title: 'Error',
        description: 'Error occurred during onboarding completion',
        type: 'error',
        value: {},
        isRedeemed: false,
      );
    }
  }

  /// Detekce onboarding personality
  Future<OnboardingPersonality> _detectOnboardingPersonality(
    String userId,
  ) async {
    // Simulace detekce personality na základě prvních interakcí
    await Future.delayed(const Duration(milliseconds: 500));

    final personalities = OnboardingPersonality.values;
    return personalities[Random().nextInt(personalities.length)];
  }

  /// Výběr questů podle personality
  List<OnboardingQuest> _selectQuestsForPersonality(
    OnboardingPersonality personality,
  ) {
    final allQuests = List<OnboardingQuest>.from(_quests);

    switch (personality) {
      case OnboardingPersonality.achiever:
        // Achievers milují výzvy a pokrok
        return allQuests
            .where((q) => q.category == 'achievement' || q.difficulty >= 3)
            .take(5)
            .toList();

      case OnboardingPersonality.explorer:
        // Explorers chtějí objevovat funkce
        return allQuests
            .where((q) => q.category == 'discovery' || q.category == 'features')
            .take(5)
            .toList();

      case OnboardingPersonality.socializer:
        // Socializers se zaměřují na sdílení
        return allQuests
            .where((q) => q.category == 'social' || q.category == 'sharing')
            .take(5)
            .toList();

      case OnboardingPersonality.competitor:
        // Casual users chtějí jednoduché úkoly
        return allQuests.where((q) => q.difficulty <= 2).take(4).toList();
    }
  }

  /// Kontrola milníků
  Future<List<OnboardingMilestone>> _checkMilestones(
    String userId,
    UserOnboardingProgress progress,
  ) async {
    final unlockedMilestones = <OnboardingMilestone>[];

    for (final milestone in _milestones) {
      if (progress.completedMilestones.contains(milestone.id)) continue;

      bool isUnlocked = false;

      // Použijeme requiredQuests místo type a requirement
      if (milestone.requiredQuests > 0) {
        isUnlocked =
            progress.completedQuests.length >= milestone.requiredQuests;
      } else {
        // Fallback na základní kontrolu
        isUnlocked = progress.completedQuests.isNotEmpty;
      }

      if (isUnlocked) {
        unlockedMilestones.add(milestone);

        // Aktualizace pokroku
        _userProgress[userId] = UserOnboardingProgress(
          userId: progress.userId,
          journeyId: progress.journeyId,
          completedQuests: progress.completedQuests,
          completedMilestones: [...progress.completedMilestones, milestone.id],
          totalPoints: progress.totalPoints,
          currentStreak: progress.currentStreak,
          startedAt: progress.startedAt,
          completedAt: progress.completedAt,
          personality: progress.personality,
          preferences: progress.preferences,
          categoryProgress: progress.categoryProgress,
        );
      }
    }

    if (unlockedMilestones.isNotEmpty) {
      await _saveUserProgress();
    }

    return unlockedMilestones;
  }

  /// Výpočet bonus XP
  int _calculateBonusXP(OnboardingQuest quest, Map<String, dynamic>? data) {
    int bonus = 0;

    // Speed bonus
    if (data?['completionTime'] != null) {
      final completionTime = data!['completionTime'] as Duration;
      final estimatedMinutes = quest.estimatedMinutes;
      if (completionTime.inMinutes < estimatedMinutes * 0.5) {
        bonus += 50; // Speed bonus
      }
    }

    // Quality bonus
    if (data?['quality'] != null) {
      final quality = data!['quality'] as double;
      if (quality > 0.8) {
        bonus += 30; // Quality bonus
      }
    }

    // First try bonus
    if (data?['attempts'] == 1) {
      bonus += 20; // First try bonus
    }

    return bonus;
  }

  /// Výpočet levelu z XP
  int _calculateLevel(int xp) {
    // Exponenciální růst: level = sqrt(xp / 100) + 1
    return (sqrt(xp / 100)).floor() + 1;
  }

  /// Získání onboarding journey
  Future<OnboardingJourney?> _getOnboardingJourney(String userId) async {
    final progress = _userProgress[userId];
    if (progress == null) return null;

    // Rekonstrukce journey z pokroku
    // V produkci by se načítalo z databáze
    return null;
  }

  /// Načítání a ukládání dat
  Future<void> _loadOnboardingQuests() async {
    await _createDefaultQuests();
  }

  Future<void> _loadOnboardingMilestones() async {
    await _createDefaultMilestones();
  }

  Future<void> _loadUserProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = prefs.getString('onboarding_progress');

      if (progressJson != null) {
        final Map<String, dynamic> data = jsonDecode(progressJson);
        _userProgress.clear();
        data.forEach((userId, progressData) {
          _userProgress[userId] = UserOnboardingProgress.fromJson(progressData);
        });
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání onboarding pokroku: $e');
    }
  }

  Future<void> _saveUserProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = _userProgress.map(
        (userId, progress) => MapEntry(userId, progress.toJson()),
      );
      await prefs.setString('onboarding_progress', jsonEncode(data));
    } catch (e) {
      debugPrint('❌ Chyba při ukládání onboarding pokroku: $e');
    }
  }

  Future<void> _createDefaultQuests() async {
    _quests.addAll([
      // Základní questy
      OnboardingQuest(
        id: 'first_entry',
        title: 'První vzpomínka',
        description: 'Napište svůj první zápis do deníku',
        category: 'writing',
        difficulty: 1,
        estimatedMinutes: 5,
        steps: [
          'Klikněte na tlačítko "Nový zápis"',
          'Napište alespoň 50 slov',
          'Přidejte náladu',
          'Uložte zápis',
        ],
        requirements: {'minWords': 50},
        rewards: {'xp': 100, 'badge': 'first_entry'},
        isOptional: false,
        prerequisites: [],
      ),

      OnboardingQuest(
        id: 'add_photo',
        title: 'Zachyťte moment',
        description: 'Přidejte fotografii k vašemu zápisu',
        category: 'media',
        difficulty: 1,
        estimatedMinutes: 3,
        steps: [
          'Otevřete váš zápis',
          'Klikněte na ikonu fotoaparátu',
          'Vyberte nebo pořiďte fotografii',
          'Uložte změny',
        ],
        requirements: {'hasPhoto': true},
        rewards: {'xp': 75, 'badge': 'photographer'},
        isOptional: false,
        prerequisites: ['first_entry'],
      ),

      OnboardingQuest(
        id: 'explore_search',
        title: 'Objevte vyhledávání',
        description: 'Vyzkoušejte pokročilé vyhledávání',
        category: 'features',
        difficulty: 2,
        estimatedMinutes: 4,
        steps: [
          'Přejděte na vyhledávání',
          'Vyzkoušejte filtr podle nálady',
          'Vyhledejte podle data',
          'Prohlédněte si výsledky',
        ],
        requirements: {'searchUsed': true},
        rewards: {'xp': 150, 'badge': 'explorer'},
        isOptional: false,
        prerequisites: ['add_photo'],
      ),

      OnboardingQuest(
        id: 'achievement_unlock',
        title: 'První úspěch',
        description: 'Odemkněte svůj první achievement',
        category: 'gamification',
        difficulty: 2,
        estimatedMinutes: 2,
        steps: [
          'Pokračujte v psaní',
          'Sledujte pokrok v achievementech',
          'Oslavte svůj úspěch!',
        ],
        requirements: {'achievementUnlocked': true},
        rewards: {'xp': 200, 'badge': 'achiever'},
        isOptional: false,
        prerequisites: ['explore_search'],
      ),

      OnboardingQuest(
        id: 'share_memory',
        title: 'Sdílejte radost',
        description: 'Sdílejte vzpomínku s přáteli',
        category: 'sharing',
        difficulty: 2,
        estimatedMinutes: 3,
        steps: [
          'Vyberte vzpomínku ke sdílení',
          'Klikněte na tlačítko sdílení',
          'Vyberte způsob sdílení',
          'Potvrďte akci',
        ],
        requirements: {'memoryShared': true},
        rewards: {'xp': 125, 'badge': 'socializer'},
        isOptional: true,
        prerequisites: ['achievement_unlock'],
      ),
    ]);
  }

  Future<void> _createDefaultMilestones() async {
    _milestones.addAll([
      OnboardingMilestone(
        id: 'first_steps',
        title: 'První kroky',
        description: 'Dokončili jste první quest',
        requiredQuests: 1,
        questIds: ['first_entry'],
        rewards: {'features': 'basic', 'badge': 'first_steps'},
        isCompleted: false,
      ),

      OnboardingMilestone(
        id: 'getting_started',
        title: 'Začínáme',
        description: 'Dokončili jste 3 questy',
        requiredQuests: 3,
        questIds: ['first_entry', 'add_photo', 'explore_search'],
        rewards: {'features': 'advanced', 'badge': 'getting_started'},
        isCompleted: false,
      ),

      OnboardingMilestone(
        id: 'xp_collector',
        title: 'Sběratel XP',
        description: 'Získali jste 500 XP',
        requiredQuests: 0,
        questIds: [],
        rewards: {'badge': 'xp_collector', 'special': 'star'},
        isCompleted: false,
      ),

      OnboardingMilestone(
        id: 'level_up',
        title: 'Levelování',
        description: 'Dosáhli jste 3. levelu',
        requiredQuests: 3,
        questIds: ['first_entry', 'add_photo', 'explore_search'],
        rewards: {'premium': 'preview', 'badge': 'level_up'},
        isCompleted: false,
      ),
    ]);
  }

  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<OnboardingQuest> get quests => List.unmodifiable(_quests);
  List<OnboardingMilestone> get milestones => List.unmodifiable(_milestones);
  UserOnboardingProgress? getUserProgress(String userId) =>
      _userProgress[userId];
}
