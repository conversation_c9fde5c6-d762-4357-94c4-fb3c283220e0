import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/business.dart';
import '../services/business_service.dart';
import '../widgets/watercolor_painters.dart';

/// 🏢 BUSINESS DASHBOARD - B2B dashboard pro firmy
class BusinessDashboardScreen extends StatefulWidget {
  final String businessId;

  const BusinessDashboardScreen({super.key, required this.businessId});

  @override
  State<BusinessDashboardScreen> createState() =>
      _BusinessDashboardScreenState();
}

class _BusinessDashboardScreenState extends State<BusinessDashboardScreen>
    with TickerProviderStateMixin {
  final BusinessService _businessService = BusinessService();
  late TabController _tabController;

  BusinessProfile? _business;
  BusinessAnalytics? _analytics;
  List<BusinessBooking> _bookings = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadBusinessData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadBusinessData() async {
    try {
      setState(() => _isLoading = true);

      await _businessService.initialize();

      // Načtení business profilu
      _business = _businessService.allBusinesses.firstWhere(
        (b) => b.id == widget.businessId,
      );

      // Načtení analytics
      _analytics = await _businessService.getBusinessAnalytics(
        widget.businessId,
        startDate: DateTime.now().subtract(const Duration(days: 30)),
        endDate: DateTime.now(),
      );

      // Načtení rezervací
      _bookings = await _businessService.getBusinessBookings(widget.businessId);

      setState(() => _isLoading = false);
    } catch (e) {
      debugPrint('❌ Chyba při načítání business dat: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomPaint(
        painter: WatercolorBusinessBackgroundPainter(),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              _buildTabBar(),
              Expanded(
                child: _isLoading ? _buildLoadingState() : _buildTabBarView(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Business Dashboard',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                if (_business != null)
                  Text(
                    _business!.name,
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: _showBusinessSettings,
            icon: const Icon(Icons.settings, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(25),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: const Color(0xFF006994),
          borderRadius: BorderRadius.circular(25),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: const Color(0xFF666666),
        labelStyle: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        tabs: const [
          Tab(icon: Icon(Icons.dashboard), text: 'Přehled'),
          Tab(icon: Icon(Icons.analytics), text: 'Analytics'),
          Tab(icon: Icon(Icons.book_online), text: 'Rezervace'),
          Tab(icon: Icon(Icons.business), text: 'Profil'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildAnalyticsTab(),
        _buildBookingsTab(),
        _buildProfileTab(),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF006994)),
      ),
    );
  }

  Widget _buildOverviewTab() {
    if (_analytics == null) return const SizedBox();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // KPI karty
          Row(
            children: [
              Expanded(
                child: _buildKPICard(
                  'Zobrazení',
                  '${_analytics!.totalViews}',
                  Icons.visibility,
                  const Color(0xFF4CAF50),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildKPICard(
                  'Kliky',
                  '${_analytics!.totalClicks}',
                  Icons.mouse,
                  const Color(0xFF2196F3),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildKPICard(
                  'Rezervace',
                  '${_analytics!.totalBookings}',
                  Icons.book_online,
                  const Color(0xFFFF9800),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildKPICard(
                  'Tržby',
                  '${_analytics!.revenue.toStringAsFixed(0)} HRK',
                  Icons.attach_money,
                  const Color(0xFF9C27B0),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Graf oblíbených časů
          _buildPopularTimesChart(),

          const SizedBox(height: 20),

          // Nedávné rezervace
          _buildRecentBookings(),
        ],
      ),
    );
  }

  Widget _buildKPICard(String title, String value, IconData icon, Color color) {
    return CustomPaint(
      painter: WatercolorBusinessCardPainter(color),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '+12%',
                    style: GoogleFonts.inter(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: GoogleFonts.playfairDisplay(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2C2C2C),
              ),
            ),
            Text(
              title,
              style: GoogleFonts.inter(
                fontSize: 12,
                color: const Color(0xFF666666),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPopularTimesChart() {
    if (_analytics == null) return const SizedBox();

    return Container(
      child: CustomPaint(
        painter: WatercolorBusinessCardPainter(const Color(0xFF006994)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Oblíbené časy',
                style: GoogleFonts.playfairDisplay(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2C2C2C),
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 200,
                child: BarChart(
                  BarChartData(
                    alignment: BarChartAlignment.spaceAround,
                    maxY: _analytics!.popularTimes.values
                        .reduce((a, b) => a > b ? a : b)
                        .toDouble(),
                    barTouchData: BarTouchData(enabled: false),
                    titlesData: FlTitlesData(
                      show: true,
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, meta) {
                            final times = _analytics!.popularTimes.keys
                                .toList();
                            if (value.toInt() < times.length) {
                              return Text(
                                times[value.toInt()],
                                style: GoogleFonts.inter(fontSize: 10),
                              );
                            }
                            return const Text('');
                          },
                        ),
                      ),
                      leftTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      topTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      rightTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                    ),
                    borderData: FlBorderData(show: false),
                    barGroups: _analytics!.popularTimes.entries.map((entry) {
                      final index = _analytics!.popularTimes.keys
                          .toList()
                          .indexOf(entry.key);
                      return BarChartGroupData(
                        x: index,
                        barRods: [
                          BarChartRodData(
                            toY: entry.value.toDouble(),
                            color: const Color(0xFF006994),
                            width: 20,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentBookings() {
    final recentBookings = _bookings.take(5).toList();

    return Container(
      child: CustomPaint(
        painter: WatercolorBusinessCardPainter(const Color(0xFF4CAF50)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Nedávné rezervace',
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2C2C2C),
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () => _tabController.animateTo(2),
                    child: Text(
                      'Zobrazit vše',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: const Color(0xFF006994),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ...recentBookings
                  .map((booking) => _buildBookingItem(booking))
                  .toList(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookingItem(BusinessBooking booking) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: _getBookingStatusColor(booking.status),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  booking.customerName,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF2C2C2C),
                  ),
                ),
                Text(
                  '${booking.serviceType} • ${booking.numberOfPeople} osob',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${booking.totalPrice.toStringAsFixed(0)} HRK',
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF2C2C2C),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    // Implementace analytics tabu
    return const Center(child: Text('Analytics Tab - Coming Soon'));
  }

  Widget _buildBookingsTab() {
    // Implementace rezervací tabu
    return const Center(child: Text('Bookings Tab - Coming Soon'));
  }

  Widget _buildProfileTab() {
    // Implementace profil tabu
    return const Center(child: Text('Profile Tab - Coming Soon'));
  }

  Color _getBookingStatusColor(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return const Color(0xFFFF9800);
      case BookingStatus.confirmed:
        return const Color(0xFF4CAF50);
      case BookingStatus.cancelled:
        return const Color(0xFFF44336);
      case BookingStatus.completed:
        return const Color(0xFF2196F3);
      case BookingStatus.noShow:
        return const Color(0xFF9E9E9E);
    }
  }

  void _showBusinessSettings() {
    // Implementace nastavení
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Nastavení podniku'),
        content: const Text('Nastavení bude implementováno v další verzi.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
