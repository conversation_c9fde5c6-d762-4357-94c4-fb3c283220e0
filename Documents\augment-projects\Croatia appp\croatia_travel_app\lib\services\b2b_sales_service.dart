import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/b2b_sales.dart';

/// 💼 B2B SALES SERVICE - B2B prodejní program pro immediate revenue
class B2BSalesService {
  static final B2BSalesService _instance = B2BSalesService._internal();
  factory B2BSalesService() => _instance;
  B2BSalesService._internal();

  bool _isInitialized = false;
  final List<B2BLead> _leads = [];
  final List<B2BProposal> _proposals = [];
  final List<B2BContract> _contracts = [];
  final List<B2BClient> _clients = [];
  final StreamController<SalesEvent> _eventController =
      StreamController.broadcast();

  /// Stream prodejních událostí
  Stream<SalesEvent> get salesEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('💼 Inicializuji B2B Sales Service...');

      await _loadSalesData();
      await _generateMockLeads();
      await _initializeSalesTargets();

      _isInitialized = true;
      debugPrint('✅ B2B Sales Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci B2B Sales: $e');
      await _generateMockLeads();
      _isInitialized = true;
    }
  }

  /// Vytvoření nového leadu
  Future<B2BLead> createLead({
    required String companyName,
    required String contactPerson,
    required String email,
    required String phone,
    required B2BSegment segment,
    required LeadSource source,
    String? notes,
    Map<String, dynamic>? customData,
  }) async {
    try {
      final lead = B2BLead(
        id: 'lead_${DateTime.now().millisecondsSinceEpoch}',
        companyName: companyName,
        contactPerson: contactPerson,
        email: email,
        phone: phone,
        segment: segment,
        source: source,
        status: LeadStatus.new_,
        estimatedValue: 0.0,
        score: _calculateLeadScore(segment, source).toDouble(),
        createdAt: DateTime.now(),
        lastContactAt: null,
        notes: notes ?? '',
        urgency: UrgencyLevel.medium,
        customFields: customData ?? {},
      );

      _leads.add(lead);
      await _saveLeads();

      _eventController.add(
        SalesEvent(
          id: 'event_${DateTime.now().millisecondsSinceEpoch}',
          type: SalesEventType.leadCreated,
          leadId: lead.id,
          description: 'New lead created: ${lead.companyName}',
          timestamp: DateTime.now(),
          userId: 'current_user',
          metadata: {'segment': segment.name, 'source': source.name},
        ),
      );

      debugPrint('🎯 Nový lead vytvořen: ${lead.companyName}');
      return lead;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření leadu: $e');
      rethrow;
    }
  }

  /// Kvalifikace leadu
  Future<bool> qualifyLead({
    required String leadId,
    required int budget,
    required int employeeCount,
    required bool hasDecisionMaker,
    required UrgencyLevel urgency,
    String? qualificationNotes,
  }) async {
    try {
      final leadIndex = _leads.indexWhere((l) => l.id == leadId);
      if (leadIndex == -1) return false;

      final lead = _leads[leadIndex];
      final qualification = LeadQualification(
        leadId: leadId,
        budgetScore: _calculateBudgetScore(budget.toDouble()),
        authorityScore: hasDecisionMaker ? 100 : 50,
        needScore: 80,
        timelineScore: _getTimelineScore(urgency),
        totalScore: 80,
        isQualified: true,
        budget: budget.toDouble(),
        employeeCount: employeeCount,
        hasDecisionMaker: hasDecisionMaker,
        urgency: urgency,
        qualifiedAt: DateTime.now(),
        qualifiedBy: 'current_user',
        notes: qualificationNotes,
      );

      final isQualified = _isLeadQualified(qualification);
      final newStatus = isQualified
          ? LeadStatus.qualified
          : LeadStatus.disqualified;

      _leads[leadIndex] = lead.copyWith(
        status: newStatus,
        qualification: qualification,
        score: _recalculateLeadScore(lead, qualification).toDouble(),
        lastContactAt: DateTime.now(),
      );

      await _saveLeads();

      _eventController.add(
        SalesEvent(
          id: 'event_${DateTime.now().millisecondsSinceEpoch}',
          type: isQualified
              ? SalesEventType.leadQualified
              : SalesEventType.leadDisqualified,
          leadId: leadId,
          description: 'Lead ${isQualified ? "qualified" : "disqualified"}',
          timestamp: DateTime.now(),
          userId: 'current_user',
          metadata: {'budget': budget, 'urgency': urgency.name},
        ),
      );

      debugPrint(
        '✅ Lead ${isQualified ? "kvalifikován" : "diskvalifikován"}: ${lead.companyName}',
      );
      return isQualified;
    } catch (e) {
      debugPrint('❌ Chyba při kvalifikaci leadu: $e');
      return false;
    }
  }

  /// Vytvoření návrhu/proposal
  Future<B2BProposal> createProposal({
    required String leadId,
    required B2BPackage package,
    required double customPrice,
    required int contractLength,
    List<String>? customFeatures,
    String? notes,
  }) async {
    try {
      final lead = _leads.firstWhere((l) => l.id == leadId);

      final proposal = B2BProposal(
        id: 'proposal_${DateTime.now().millisecondsSinceEpoch}',
        leadId: leadId,
        title: 'Proposal for ${lead.companyName}',
        description: 'Custom proposal for ${package.name} package',
        totalValue: customPrice * contractLength,
        package: package,
        status: ProposalStatus.draft,
        createdAt: DateTime.now(),
        validUntil: DateTime.now().add(const Duration(days: 30)),
        companyName: lead.companyName,
        customPrice: customPrice,
        contractLength: contractLength,
        terms: notes,
      );

      _proposals.add(proposal);
      await _saveProposals();

      // Aktualizace leadu
      final leadIndex = _leads.indexWhere((l) => l.id == leadId);
      _leads[leadIndex] = lead.copyWith(
        status: LeadStatus.proposal,
        lastContactAt: DateTime.now(),
      );
      await _saveLeads();

      _eventController.add(
        SalesEvent(
          id: 'event_${DateTime.now().millisecondsSinceEpoch}',
          type: SalesEventType.proposalCreated,
          leadId: leadId,
          description: 'Proposal created for ${lead.companyName}',
          timestamp: DateTime.now(),
          userId: 'current_user',
          metadata: {
            'package': package.name,
            'price': customPrice,
            'proposalId': proposal.id,
          },
        ),
      );

      debugPrint('📋 Návrh vytvořen pro: ${lead.companyName}');
      return proposal;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření návrhu: $e');
      rethrow;
    }
  }

  /// Odeslání návrhu klientovi
  Future<bool> sendProposal({
    required String proposalId,
    String? emailTemplate,
    List<String>? attachments,
  }) async {
    try {
      final proposalIndex = _proposals.indexWhere((p) => p.id == proposalId);
      if (proposalIndex == -1) return false;

      final proposal = _proposals[proposalIndex];

      // Simulace odeslání emailu
      await _sendProposalEmail(proposal, emailTemplate, attachments);

      _proposals[proposalIndex] = proposal.copyWith(
        status: ProposalStatus.sent,
        sentAt: DateTime.now(),
      );
      await _saveProposals();

      _eventController.add(
        SalesEvent(
          id: 'event_${DateTime.now().millisecondsSinceEpoch}',
          type: SalesEventType.proposalSent,
          leadId: proposal.leadId,
          description: 'Proposal sent to ${proposal.companyName}',
          timestamp: DateTime.now(),
          userId: 'current_user',
          metadata: {'method': 'email', 'proposalId': proposalId},
        ),
      );

      debugPrint('📧 Návrh odeslán: ${proposal.companyName}');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při odesílání návrhu: $e');
      return false;
    }
  }

  /// Uzavření obchodu
  Future<B2BContract?> closeDeal({
    required String proposalId,
    required DateTime startDate,
    required PaymentTerms paymentTerms,
    String? contractNotes,
    List<String>? additionalTerms,
  }) async {
    try {
      final proposal = _proposals.firstWhere((p) => p.id == proposalId);
      final lead = _leads.firstWhere((l) => l.id == proposal.leadId);

      final contract = B2BContract(
        id: 'contract_${DateTime.now().millisecondsSinceEpoch}',
        proposalId: proposalId,
        clientId: 'client_${DateTime.now().millisecondsSinceEpoch}',
        title: 'Contract for ${proposal.companyName}',
        value: proposal.totalValue,
        status: ContractStatus.active,
        startDate: startDate,
        endDate: startDate.add(
          Duration(days: (proposal.contractLength ?? 12) * 30),
        ),
        paymentTerms: paymentTerms,
      );

      _contracts.add(contract);
      await _saveContracts();

      // Vytvoření B2B klienta
      final client = B2BClient(
        id: 'client_${DateTime.now().millisecondsSinceEpoch}',
        companyName: proposal.companyName ?? 'Unknown Company',
        industry: 'Tourism',
        employeeCount: 50,
        annualRevenue: (proposal.customPrice ?? 0) * 12,
        primaryContact: lead.contactPerson,
        email: lead.email,
        phone: lead.phone,
        address: 'Croatia',
        tier: ClientTier.bronze,
        onboardedAt: DateTime.now(),
        contractIds: [contract.id],
      );

      _clients.add(client);
      await _saveClients();

      // Aktualizace statusů
      final proposalIndex = _proposals.indexWhere((p) => p.id == proposalId);
      _proposals[proposalIndex] = proposal.copyWith(
        status: ProposalStatus.accepted,
      );

      final leadIndex = _leads.indexWhere((l) => l.id == proposal.leadId);
      _leads[leadIndex] = lead.copyWith(status: LeadStatus.closed);

      await _saveProposals();
      await _saveLeads();

      _eventController.add(
        SalesEvent(
          id: 'event_${DateTime.now().millisecondsSinceEpoch}',
          type: SalesEventType.dealClosed,
          leadId: proposal.leadId,
          description: 'Deal closed for ${proposal.companyName}',
          timestamp: DateTime.now(),
          userId: 'current_user',
          metadata: {
            'value': contract.value,
            'package': proposal.package.name,
            'proposalId': proposalId,
            'contractId': contract.id,
          },
        ),
      );

      debugPrint(
        '🎉 Obchod uzavřen: ${proposal.companyName} - ${contract.value}€',
      );
      return contract;
    } catch (e) {
      debugPrint('❌ Chyba při uzavírání obchodu: $e');
      return null;
    }
  }

  /// Získání sales dashboardu
  Future<SalesDashboard> getSalesDashboard({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final now = DateTime.now();
    final from = fromDate ?? DateTime(now.year, now.month, 1);
    final to = toDate ?? now;

    // Filtrování dat podle období
    final periodLeads = _leads
        .where(
          (l) =>
              l.createdAt.isAfter(from.subtract(const Duration(days: 1))) &&
              l.createdAt.isBefore(to.add(const Duration(days: 1))),
        )
        .toList();

    final periodContracts = _contracts
        .where(
          (c) =>
              c.startDate.isAfter(from.subtract(const Duration(days: 1))) &&
              c.startDate.isBefore(to.add(const Duration(days: 1))),
        )
        .toList();

    // Výpočet metrik
    final totalRevenue = periodContracts.fold<double>(
      0,
      (sum, c) => sum + c.value,
    );
    final conversionRate = periodLeads.isNotEmpty
        ? periodContracts.length / periodLeads.length
        : 0.0;

    final averageDealSize = periodContracts.isNotEmpty
        ? totalRevenue / periodContracts.length
        : 0.0;

    return SalesDashboard(
      generatedAt: DateTime.now(),
      period: ReportPeriod.custom,
      totalRevenue: totalRevenue,
      totalLeads: periodLeads.length,
      convertedLeads: periodContracts.length,
      conversionRate: conversionRate,
      activeProposals: _proposals
          .where((p) => p.status == ProposalStatus.sent)
          .length,
      wonDeals: periodContracts.length,
      lostDeals: _proposals
          .where((p) => p.status == ProposalStatus.rejected)
          .length,
      averageDealSize: averageDealSize,
      revenueBySegment: _getRevenueBySegment(periodContracts),
      leadsBySource: _getLeadsBySource(periodLeads),
      topLeads: periodLeads.take(5).toList(),
      recentProposals: _proposals.take(5).toList(),
    );
  }

  /// Generování sales reportu
  Future<SalesReport> generateSalesReport({
    required ReportPeriod period,
    required List<SalesMetricType> metrics,
  }) async {
    final now = DateTime.now();
    DateTime fromDate;
    DateTime toDate = now;

    switch (period) {
      case ReportPeriod.thisMonth:
        fromDate = DateTime(now.year, now.month, 1);
        break;
      case ReportPeriod.lastMonth:
        fromDate = DateTime(now.year, now.month - 1, 1);
        toDate = DateTime(
          now.year,
          now.month,
          1,
        ).subtract(const Duration(days: 1));
        break;
      case ReportPeriod.thisQuarter:
        final quarterStart = ((now.month - 1) ~/ 3) * 3 + 1;
        fromDate = DateTime(now.year, quarterStart, 1);
        break;
      case ReportPeriod.thisYear:
        fromDate = DateTime(now.year, 1, 1);
        break;
      case ReportPeriod.daily:
      case ReportPeriod.weekly:
      case ReportPeriod.monthly:
      case ReportPeriod.quarterly:
      case ReportPeriod.yearly:
      case ReportPeriod.custom:
        fromDate = DateTime(now.year, now.month, 1);
        break;
    }

    final dashboard = await getSalesDashboard(
      fromDate: fromDate,
      toDate: toDate,
    );

    return SalesReport(
      id: 'report_${DateTime.now().millisecondsSinceEpoch}',
      period: period,
      fromDate: fromDate,
      toDate: toDate,
      metrics: metrics,
      dashboard: dashboard,
      insights: _generateSalesInsights(dashboard),
      recommendations: _generateSalesRecommendations(dashboard),
      generatedAt: DateTime.now(),
    );
  }

  /// Automatické lead scoring
  int _calculateLeadScore(B2BSegment segment, LeadSource source) {
    int score = 50; // Base score

    // Segment scoring
    switch (segment) {
      case B2BSegment.tourismBoard:
        score += 30;
        break;
      case B2BSegment.travelAgency:
        score += 25;
        break;
      case B2BSegment.hotel:
        score += 20;
        break;
      case B2BSegment.restaurant:
        score += 15;
        break;
      case B2BSegment.eventOrganizer:
        score += 20;
        break;
      case B2BSegment.enterprise:
        score += 25;
        break;
      case B2BSegment.startup:
        score += 10;
        break;
      case B2BSegment.smallBusiness:
        score += 15;
        break;
      case B2BSegment.mediumBusiness:
        score += 20;
        break;
      case B2BSegment.government:
        score += 30;
        break;
    }

    // Source scoring
    switch (source) {
      case LeadSource.referral:
        score += 20;
        break;
      case LeadSource.inbound:
        score += 15;
        break;
      case LeadSource.coldOutreach:
        score += 5;
        break;
      case LeadSource.event:
        score += 10;
        break;
      case LeadSource.partnership:
        score += 25;
        break;
      case LeadSource.website:
        score += 10;
        break;
      case LeadSource.socialMedia:
        score += 8;
        break;
      case LeadSource.advertising:
        score += 12;
        break;
    }

    return score.clamp(0, 100);
  }

  int _recalculateLeadScore(B2BLead lead, LeadQualification qualification) {
    int score = (lead.score ?? 50).toInt();

    // Budget scoring
    if ((qualification.budget ?? 0) >= 10000) {
      score += 20;
    } else if ((qualification.budget ?? 0) >= 5000) {
      score += 15;
    } else if ((qualification.budget ?? 0) >= 2000) {
      score += 10;
    } else if ((qualification.budget ?? 0) >= 1000) {
      score += 5;
    }

    // Company size scoring
    if ((qualification.employeeCount ?? 0) >= 500) {
      score += 15;
    } else if ((qualification.employeeCount ?? 0) >= 100) {
      score += 10;
    } else if ((qualification.employeeCount ?? 0) >= 50) {
      score += 5;
    }

    // Decision maker
    if (qualification.hasDecisionMaker == true) {
      score += 15;
    }

    // Urgency
    switch (qualification.urgency) {
      case UrgencyLevel.critical:
        score += 25;
        break;
      case UrgencyLevel.high:
        score += 20;
        break;
      case UrgencyLevel.medium:
        score += 10;
        break;
      case UrgencyLevel.low:
        score += 0;
        break;
      case null:
        score += 0;
        break;
    }

    return score.clamp(0, 100);
  }

  bool _isLeadQualified(LeadQualification qualification) {
    return (qualification.budget ?? 0) >= 1000 &&
        (qualification.employeeCount ?? 0) >= 10 &&
        (qualification.hasDecisionMaker ?? false);
  }

  Map<String, int> _getLeadsBySource(List<B2BLead> leads) {
    final bySource = <String, int>{};
    for (final lead in leads) {
      final sourceName = lead.source.name;
      bySource[sourceName] = (bySource[sourceName] ?? 0) + 1;
    }
    return bySource;
  }

  Map<String, double> _getRevenueBySegment(List<B2BContract> contracts) {
    final bySegment = <String, double>{};
    for (final contract in contracts) {
      // Najdeme proposal a pak lead
      final proposal = _proposals.firstWhere(
        (p) => p.id == contract.proposalId,
      );
      final lead = _leads.firstWhere((l) => l.id == proposal.leadId);
      final segmentName = lead.segment.name;
      bySegment[segmentName] = (bySegment[segmentName] ?? 0) + contract.value;
    }
    return bySegment;
  }

  List<String> _generateSalesInsights(SalesDashboard dashboard) {
    final insights = <String>[];

    if (dashboard.conversionRate > 0.2) {
      insights.add(
        'Výborná konverzní míra ${(dashboard.conversionRate * 100).toStringAsFixed(1)}%',
      );
    } else if (dashboard.conversionRate < 0.1) {
      insights.add('Nízká konverzní míra - potřeba zlepšit kvalifikaci leadů');
    }

    if (dashboard.averageDealSize > 5000) {
      insights.add(
        'Vysoká průměrná hodnota obchodu - zaměřte se na enterprise klienty',
      );
    }

    // Odstraníme averageSalesCycle check - není v SalesDashboard
    // if (dashboard.averageSalesCycle.inDays > 60) {
    //   insights.add('Dlouhý sales cycle - optimalizujte proces');
    // }

    return insights;
  }

  List<String> _generateSalesRecommendations(SalesDashboard dashboard) {
    final recommendations = <String>[];

    if (dashboard.convertedLeads < dashboard.totalLeads * 0.3) {
      recommendations.add('Zlepšete lead qualification process');
    }

    // Odstraníme pipelineValue check - není v SalesDashboard
    // if (dashboard.pipelineValue < dashboard.monthlyRecurringRevenue * 3) {
    //   recommendations.add('Zvyšte aktivitu v generování leadů');
    // }

    recommendations.addAll([
      'Implementujte automated follow-up sekvence',
      'Vytvořte case studies pro každý segment',
      'Optimalizujte pricing strategy na základě dat',
    ]);

    return recommendations;
  }

  /// Simulace odeslání emailu
  Future<void> _sendProposalEmail(
    B2BProposal proposal,
    String? template,
    List<String>? attachments,
  ) async {
    // Simulace email delivery
    await Future.delayed(const Duration(seconds: 1));
    debugPrint('📧 Email odeslán: ${proposal.companyName}');
  }

  /// Načítání a ukládání dat
  Future<void> _loadSalesData() async {
    // Načtení z SharedPreferences
  }

  Future<void> _saveLeads() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'b2b_leads',
        jsonEncode(_leads.map((l) => l.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání leadů: $e');
    }
  }

  Future<void> _saveProposals() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'b2b_proposals',
        jsonEncode(_proposals.map((p) => p.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání návrhů: $e');
    }
  }

  Future<void> _saveContracts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'b2b_contracts',
        jsonEncode(_contracts.map((c) => c.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání smluv: $e');
    }
  }

  Future<void> _saveClients() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'b2b_clients',
        jsonEncode(_clients.map((c) => c.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání klientů: $e');
    }
  }

  Future<void> _generateMockLeads() async {
    // Generování mock leadů pro demo
    await createLead(
      companyName: 'Croatian Tourism Board',
      contactPerson: 'Ana Marić',
      email: '<EMAIL>',
      phone: '+385 1 234 5678',
      segment: B2BSegment.tourismBoard,
      source: LeadSource.inbound,
      notes: 'Zájem o enterprise řešení pro promoci Chorvatska',
    );

    await createLead(
      companyName: 'Adriatic Travel Agency',
      contactPerson: 'Marko Novak',
      email: '<EMAIL>',
      phone: '+385 21 345 678',
      segment: B2BSegment.travelAgency,
      source: LeadSource.referral,
      notes: 'Doporučení od stávajícího klienta',
    );
  }

  Future<void> _initializeSalesTargets() async {
    // Inicializace sales cílů
  }

  /// Výpočet budget score
  int _calculateBudgetScore(double budget) {
    if (budget >= 50000) return 100;
    if (budget >= 20000) return 80;
    if (budget >= 10000) return 60;
    if (budget >= 5000) return 40;
    if (budget >= 1000) return 20;
    return 10;
  }

  /// Výpočet timeline score
  int _getTimelineScore(UrgencyLevel urgency) {
    switch (urgency) {
      case UrgencyLevel.critical:
        return 100;
      case UrgencyLevel.high:
        return 90;
      case UrgencyLevel.medium:
        return 70;
      case UrgencyLevel.low:
        return 40;
    }
  }

  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<B2BLead> get leads => List.unmodifiable(_leads);
  List<B2BProposal> get proposals => List.unmodifiable(_proposals);
  List<B2BContract> get contracts => List.unmodifiable(_contracts);
  List<B2BClient> get clients => List.unmodifiable(_clients);
}
