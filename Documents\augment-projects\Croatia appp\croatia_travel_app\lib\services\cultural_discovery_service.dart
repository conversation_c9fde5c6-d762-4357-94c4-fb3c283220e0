import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/cultural_site.dart';

/// Služba pro objevování kulturních míst v Chorvatsku
/// Právně bezpečné řešení bez API závislostí
class CulturalDiscoveryService {
  static final CulturalDiscoveryService _instance =
      CulturalDiscoveryService._internal();
  factory CulturalDiscoveryService() => _instance;
  CulturalDiscoveryService._internal();

  // Cache pro kulturní místa
  List<CulturalSite>? _cachedCulturalSites;
  List<CulturalEvent>? _cachedCulturalEvents;
  List<CulturalRoute>? _cachedCulturalRoutes;
  DateTime? _lastCacheUpdate;
  final Duration _cacheValidDuration = const Duration(hours: 6);

  /// Získá všechna kulturní místa
  Future<List<CulturalSite>> getAllCulturalSites() async {
    if (_isCacheValid() && _cachedCulturalSites != null) {
      return _cachedCulturalSites!;
    }

    try {
      // Simulace načítání z lokální databáze
      await Future.delayed(const Duration(milliseconds: 800));

      _cachedCulturalSites = _generateSampleCulturalSites();
      _lastCacheUpdate = DateTime.now();

      return _cachedCulturalSites!;
    } catch (e) {
      debugPrint('Chyba při načítání kulturních míst: $e');
      return [];
    }
  }

  /// Získá všechny kulturní události
  Future<List<CulturalEvent>> getAllCulturalEvents() async {
    if (_isCacheValid() && _cachedCulturalEvents != null) {
      return _cachedCulturalEvents!;
    }

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      _cachedCulturalEvents = _generateSampleCulturalEvents();
      _lastCacheUpdate = DateTime.now();

      return _cachedCulturalEvents!;
    } catch (e) {
      debugPrint('Chyba při načítání kulturních událostí: $e');
      return [];
    }
  }

  /// Vyhledá kulturní místa podle kritérií
  Future<List<CulturalSite>> searchCulturalSites({
    String? query,
    String? region,
    CulturalSiteType? siteType,
    HistoricalPeriod? historicalPeriod,
    ArchitecturalStyle? architecturalStyle,
    bool? isUnescoSite,
    bool? hasGuidedTours,
    bool? isAccessible,
    double? latitude,
    double? longitude,
    double? maxDistance,
    double? minRating,
  }) async {
    final allSites = await getAllCulturalSites();

    return allSites.where((site) {
      // Text search
      if (query != null && query.isNotEmpty) {
        final searchLower = query.toLowerCase();
        if (!site.name.toLowerCase().contains(searchLower) &&
            !site.description.toLowerCase().contains(searchLower) &&
            !site.address.toLowerCase().contains(searchLower)) {
          return false;
        }
      }

      // Region filter
      if (region != null && region != 'all' && site.region != region) {
        return false;
      }

      // Site type filter
      if (siteType != null && site.siteType != siteType) {
        return false;
      }

      // Historical period filter
      if (historicalPeriod != null &&
          site.historicalPeriod != historicalPeriod) {
        return false;
      }

      // Architectural style filter
      if (architecturalStyle != null &&
          site.architecturalStyle != architecturalStyle) {
        return false;
      }

      // Feature filters
      if (isUnescoSite == true && !site.isUnescoSite) return false;
      if (hasGuidedTours == true && !site.hasGuidedTours) return false;
      if (isAccessible == true && !site.isAccessible) return false;

      // Distance filter
      if (latitude != null && longitude != null && maxDistance != null) {
        final distance = site.distanceFrom(latitude, longitude);
        if (distance > maxDistance) {
          return false;
        }
      }

      // Rating filter
      if (minRating != null && site.rating < minRating) {
        return false;
      }

      return true;
    }).toList();
  }

  /// Získá UNESCO památky
  Future<List<CulturalSite>> getUnescoSites({int limit = 20}) async {
    final allSites = await getAllCulturalSites();

    final unescoSites = allSites.where((s) => s.isUnescoSite).toList();
    unescoSites.sort((a, b) => b.rating.compareTo(a.rating));

    return unescoSites.take(limit).toList();
  }

  /// Získá top hodnocená kulturní místa
  Future<List<CulturalSite>> getTopRatedCulturalSites({int limit = 20}) async {
    final allSites = await getAllCulturalSites();

    final topRated = allSites.where((s) => s.rating >= 4.5).toList();
    topRated.sort((a, b) => b.rating.compareTo(a.rating));

    return topRated.take(limit).toList();
  }

  /// Získá rodinná kulturní místa
  Future<List<CulturalSite>> getFamilyFriendlyCulturalSites({
    int limit = 20,
  }) async {
    final allSites = await getAllCulturalSites();

    final familyFriendly = allSites.where((s) => s.isFamilyFriendly).toList();
    familyFriendly.sort((a, b) => b.rating.compareTo(a.rating));

    return familyFriendly.take(limit).toList();
  }

  /// Získá historicky významná místa
  Future<List<CulturalSite>> getHistoricallySignificantSites({
    int limit = 20,
  }) async {
    final allSites = await getAllCulturalSites();

    final significant = allSites
        .where((s) => s.isHistoricallySignificant)
        .toList();
    significant.sort((a, b) => b.rating.compareTo(a.rating));

    return significant.take(limit).toList();
  }

  /// Získá kulturní místa v blízkosti
  Future<List<CulturalSite>> getNearbyCulturalSites(
    double latitude,
    double longitude, {
    double maxDistance = 25.0, // km
    int limit = 20,
  }) async {
    final allSites = await getAllCulturalSites();

    final nearby = allSites
        .map((site) {
          final distance = site.distanceFrom(latitude, longitude);
          return MapEntry(site, distance);
        })
        .where((entry) => entry.value <= maxDistance)
        .toList();

    nearby.sort((a, b) => a.value.compareTo(b.value));

    return nearby.take(limit).map((entry) => entry.key).toList();
  }

  /// Získá aktuální kulturní události
  Future<List<CulturalEvent>> getCurrentCulturalEvents({int limit = 20}) async {
    final allEvents = await getAllCulturalEvents();

    final current = allEvents
        .where((e) => e.isOngoing || e.isUpcoming)
        .toList();
    current.sort((a, b) => a.startDate.compareTo(b.startDate));

    return current.take(limit).toList();
  }

  /// Získá kulturní trasy
  Future<List<CulturalRoute>> getCulturalRoutes({int limit = 10}) async {
    _cachedCulturalRoutes ??= _generateSampleCulturalRoutes();

    final routes = List<CulturalRoute>.from(_cachedCulturalRoutes!);
    routes.sort((a, b) => b.rating.compareTo(a.rating));

    return routes.take(limit).toList();
  }

  /// Získá statistiky kulturních míst
  Future<CulturalStatistics> getCulturalStatistics() async {
    final allSites = await getAllCulturalSites();
    final allEvents = await getAllCulturalEvents();

    return CulturalStatistics(
      totalSites: allSites.length,
      unescoSites: allSites.where((s) => s.isUnescoSite).length,
      topRated: allSites.where((s) => s.rating >= 4.5).length,
      familyFriendly: allSites.where((s) => s.isFamilyFriendly).length,
      historicallySignificant: allSites
          .where((s) => s.isHistoricallySignificant)
          .length,
      byRegion: _getSitesByRegion(allSites),
      byType: _getSitesByType(allSites),
      byPeriod: _getSitesByPeriod(allSites),
      totalEvents: allEvents.length,
      currentEvents: allEvents.where((e) => e.isOngoing || e.isUpcoming).length,
    );
  }

  /// Kontroluje platnost cache
  bool _isCacheValid() {
    return _lastCacheUpdate != null &&
        DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration;
  }

  /// Generuje ukázková data kulturních míst
  List<CulturalSite> _generateSampleCulturalSites() {
    final sites = <CulturalSite>[];

    // UNESCO World Heritage Sites
    sites.addAll([
      _createCulturalSite(
        'Dubrovník - Staré město',
        'Středověké město obklopené mohutnými hradbami, perla Jadranu',
        'Stradun, Dubrovník',
        42.6414,
        18.1081,
        'dubrovnik',
        CulturalSiteType.historicTown,
        HistoricalPeriod.medieval,
        ArchitecturalStyle.gothic,
        4.9,
        1234,
        isUnescoSite: true,
        unescoYear: '1979',
        hasGuidedTours: true,
        hasAudioGuide: true,
        isAccessible: true,
      ),
      _createCulturalSite(
        'Diocletianův palác',
        'Římský palác císaře Diocletiana, srdce historického Splitu',
        'Dioklecijanova ul., Split',
        43.5081,
        16.4402,
        'dalmatia',
        CulturalSiteType.palace,
        HistoricalPeriod.roman,
        ArchitecturalStyle.roman,
        4.8,
        987,
        isUnescoSite: true,
        unescoYear: '1979',
        hasGuidedTours: true,
        hasAudioGuide: true,
        isAccessible: false,
      ),
      _createCulturalSite(
        'Katedrála sv. Jakuba',
        'Gotická katedrála v Šibeniku, mistrovské dílo kamenického umění',
        'Trg Republike Hrvatske, Šibenik',
        43.7350,
        15.8952,
        'dalmatia',
        CulturalSiteType.church,
        HistoricalPeriod.medieval,
        ArchitecturalStyle.gothic,
        4.7,
        456,
        isUnescoSite: true,
        unescoYear: '2000',
        hasGuidedTours: true,
        hasAudioGuide: false,
        isAccessible: true,
      ),
    ]);

    // Castles and Fortresses
    sites.addAll([
      _createCulturalSite(
        'Hrad Motovun',
        'Středověký hrad na kopci s výhledem na istrijskou krajinu',
        'Motovun, Istrie',
        45.3367,
        13.8289,
        'istria',
        CulturalSiteType.castle,
        HistoricalPeriod.medieval,
        ArchitecturalStyle.gothic,
        4.6,
        234,
        isUnescoSite: false,
        hasGuidedTours: true,
        hasAudioGuide: false,
        isAccessible: false,
      ),
      _createCulturalSite(
        'Pevnost Nehaj',
        'Renesanční pevnost v Seni, symbol odporu proti Turkům',
        'Nehajska ul., Senj',
        44.9894,
        14.9058,
        'kvarner',
        CulturalSiteType.fortress,
        HistoricalPeriod.renaissance,
        ArchitecturalStyle.renaissance,
        4.4,
        167,
        isUnescoSite: false,
        hasGuidedTours: true,
        hasAudioGuide: false,
        isAccessible: true,
      ),
      _createCulturalSite(
        'Hrad Trakošćan',
        'Romantický hrad obklopený jezerem a parkem',
        'Trakošćan, Zagorje',
        46.2167,
        15.9667,
        'zagreb',
        CulturalSiteType.castle,
        HistoricalPeriod.medieval,
        ArchitecturalStyle.gothic,
        4.5,
        345,
        isUnescoSite: false,
        hasGuidedTours: true,
        hasAudioGuide: true,
        isAccessible: false,
      ),
    ]);

    // Churches and Monasteries
    sites.addAll([
      _createCulturalSite(
        'Eufraziova bazilika',
        'Byzantská bazilika s nádhernými mozaikami v Poreči',
        'Eufrazijeva ul., Poreč',
        45.2258,
        13.5944,
        'istria',
        CulturalSiteType.church,
        HistoricalPeriod.byzantine,
        ArchitecturalStyle.byzantine,
        4.8,
        567,
        isUnescoSite: true,
        unescoYear: '1997',
        hasGuidedTours: true,
        hasAudioGuide: true,
        isAccessible: true,
      ),
      _createCulturalSite(
        'Klášter na ostrově Mljet',
        'Benediktinský klášter na ostrově uprostřed slané laguna',
        'Mljet, Dubrovník',
        42.7833,
        17.3167,
        'dubrovnik',
        CulturalSiteType.monastery,
        HistoricalPeriod.medieval,
        ArchitecturalStyle.romanesque,
        4.3,
        123,
        isUnescoSite: false,
        hasGuidedTours: false,
        hasAudioGuide: false,
        isAccessible: false,
      ),
    ]);

    // Museums
    sites.addAll([
      _createCulturalSite(
        'Muzeum zlomených vztahů',
        'Unikátní muzeum věnované ukončeným vztahům',
        'Ćirilometodska ul. 2, Zagreb',
        45.8150,
        15.9819,
        'zagreb',
        CulturalSiteType.museum,
        HistoricalPeriod.contemporary,
        ArchitecturalStyle.modern,
        4.6,
        789,
        isUnescoSite: false,
        hasGuidedTours: true,
        hasAudioGuide: true,
        isAccessible: true,
      ),
      _createCulturalSite(
        'Archeologické muzeum Zadar',
        'Bohatá sbírka římských a ilyrských artefaktů',
        'Trg opatice Čike 1, Zadar',
        44.1194,
        15.2314,
        'dalmatia',
        CulturalSiteType.museum,
        HistoricalPeriod.roman,
        ArchitecturalStyle.modern,
        4.2,
        156,
        isUnescoSite: false,
        hasGuidedTours: true,
        hasAudioGuide: false,
        isAccessible: true,
      ),
    ]);

    // Přidá náhodná kulturní místa
    for (int i = 0; i < 5; i++) {
      sites.add(_createRandomCulturalSite(Random()));
    }

    return sites;
  }

  CulturalSite _createCulturalSite(
    String name,
    String description,
    String address,
    double latitude,
    double longitude,
    String region,
    CulturalSiteType siteType,
    HistoricalPeriod historicalPeriod,
    ArchitecturalStyle architecturalStyle,
    double rating,
    int reviewCount, {
    bool isUnescoSite = false,
    String? unescoYear,
    bool hasGuidedTours = false,
    bool hasAudioGuide = false,
    bool isAccessible = false,
  }) {
    return CulturalSite(
      id: 'cultural_${name.toLowerCase().replaceAll(' ', '_').replaceAll('-', '_')}',
      name: name,
      description: description,
      address: address,
      latitude: latitude,
      longitude: longitude,
      region: region,
      siteType: siteType,
      historicalPeriod: historicalPeriod,
      architecturalStyle: architecturalStyle,
      rating: rating,
      reviewCount: reviewCount,
      isUnescoSite: isUnescoSite,
      unescoYear: unescoYear,
      features: _generateCulturalFeatures(),
      openingHours: '9:00 - 17:00',
      ticketPrice: isUnescoSite ? '50 HRK' : '30 HRK',
      phone:
          '+385 ${Random().nextInt(99)} ${Random().nextInt(999)} ${Random().nextInt(999)}',
      website: 'https://www.${name.toLowerCase().replaceAll(' ', '')}.hr',
      hasGuidedTours: hasGuidedTours,
      hasAudioGuide: hasAudioGuide,
      isAccessible: isAccessible,
      hasParking: Random().nextBool(),
      hasGiftShop: hasGuidedTours,
      hasCafe: hasGuidedTours,
      photos: [],
      virtualTours: [],
      bestTimeToVisit: 'duben-říjen',
      estimatedVisitDuration: 60 + Random().nextInt(120),
      nearbyAttractions: [],
      lastUpdated: DateTime.now(),
    );
  }

  CulturalSite _createRandomCulturalSite(Random random) {
    final regions = ['dalmatia', 'istria', 'kvarner', 'dubrovnik', 'zagreb'];
    final siteTypes = [
      CulturalSiteType.church,
      CulturalSiteType.museum,
      CulturalSiteType.castle,
      CulturalSiteType.gallery,
    ];
    final periods = [
      HistoricalPeriod.medieval,
      HistoricalPeriod.renaissance,
      HistoricalPeriod.baroque,
      HistoricalPeriod.modern,
    ];
    final styles = [
      ArchitecturalStyle.gothic,
      ArchitecturalStyle.renaissance,
      ArchitecturalStyle.baroque,
      ArchitecturalStyle.modern,
    ];

    final names = [
      'Kostel sv. Petra',
      'Muzeum historie',
      'Galerie umění',
      'Hrad na kopci',
      'Klášter sv. Anny',
      'Archeologické naleziště',
      'Kulturní centrum',
      'Památník',
    ];

    final region = regions[random.nextInt(regions.length)];
    final name =
        '${names[random.nextInt(names.length)]} ${random.nextInt(100)}';

    return CulturalSite(
      id: 'cultural_random_${random.nextInt(10000)}',
      name: name,
      description: 'Významné kulturní místo s bohatou historií',
      address: 'Náhodná adresa ${random.nextInt(100)}, Chorvatsko',
      latitude: 42.0 + random.nextDouble() * 4.0,
      longitude: 13.0 + random.nextDouble() * 6.0,
      region: region,
      siteType: siteTypes[random.nextInt(siteTypes.length)],
      historicalPeriod: periods[random.nextInt(periods.length)],
      architecturalStyle: styles[random.nextInt(styles.length)],
      rating: 3.5 + random.nextDouble() * 1.5,
      reviewCount: 20 + random.nextInt(200),
      isUnescoSite: false,
      features: _generateCulturalFeatures(),
      openingHours: '9:00 - 17:00',
      ticketPrice: '${20 + random.nextInt(40)} HRK',
      phone:
          '+385 ${random.nextInt(99)} ${random.nextInt(999)} ${random.nextInt(999)}',
      hasGuidedTours: random.nextBool(),
      hasAudioGuide: random.nextBool(),
      isAccessible: random.nextBool(),
      hasParking: random.nextBool(),
      hasGiftShop: random.nextBool(),
      hasCafe: random.nextBool(),
      photos: [],
      virtualTours: [],
      bestTimeToVisit: 'celoročně',
      estimatedVisitDuration: 30 + random.nextInt(90),
      nearbyAttractions: [],
      lastUpdated: DateTime.now(),
    );
  }

  List<String> _generateCulturalFeatures() {
    return [
      'historical_significance',
      'architectural_beauty',
      'cultural_importance',
    ];
  }

  List<CulturalEvent> _generateSampleCulturalEvents() {
    final events = <CulturalEvent>[];
    final now = DateTime.now();

    events.addAll([
      CulturalEvent(
        id: 'event_dubrovnik_summer',
        name: 'Dubrovnické letní hry',
        description: 'Prestižní festival divadla, hudby a tance',
        location: 'Dubrovník',
        startDate: DateTime(now.year, 7, 10),
        endDate: DateTime(now.year, 8, 25),
        eventType: CulturalEventType.festival,
        ticketPrice: '100-500 HRK',
        website: 'https://www.dubrovnik-festival.hr',
        photos: [],
        isRecurring: true,
        recurrencePattern: 'annually',
      ),
      CulturalEvent(
        id: 'event_pula_film',
        name: 'Pulský filmový festival',
        description: 'Nejstarší filmový festival v Chorvatsku',
        location: 'Pula',
        startDate: DateTime(now.year, 7, 15),
        endDate: DateTime(now.year, 7, 22),
        eventType: CulturalEventType.festival,
        ticketPrice: '50-200 HRK',
        website: 'https://www.pulafilmfestival.hr',
        photos: [],
        isRecurring: true,
        recurrencePattern: 'annually',
      ),
    ]);

    return events;
  }

  List<CulturalRoute> _generateSampleCulturalRoutes() {
    return [
      CulturalRoute(
        id: 'route_unesco_dalmatia',
        name: 'UNESCO památky Dalmácie',
        description: 'Objevte nejcennější kulturní poklady Dalmácie',
        culturalSiteIds: [
          'cultural_dubrovník_staré_město',
          'cultural_diocletianův_palác',
          'cultural_katedrála_sv._jakuba',
        ],
        estimatedDuration: 6,
        difficulty: 'moderate',
        theme: 'UNESCO',
        rating: 4.8,
        reviewCount: 156,
      ),
      CulturalRoute(
        id: 'route_istrian_heritage',
        name: 'Istrijské kulturní dědictví',
        description: 'Středověké hrady a byzantské kostely Istrie',
        culturalSiteIds: [
          'cultural_hrad_motovun',
          'cultural_eufraziova_bazilika',
        ],
        estimatedDuration: 4,
        difficulty: 'easy',
        theme: 'medieval',
        rating: 4.5,
        reviewCount: 89,
      ),
    ];
  }

  Map<String, int> _getSitesByRegion(List<CulturalSite> sites) {
    final Map<String, int> result = {};
    for (final site in sites) {
      result[site.region] = (result[site.region] ?? 0) + 1;
    }
    return result;
  }

  Map<String, int> _getSitesByType(List<CulturalSite> sites) {
    final Map<String, int> result = {};
    for (final site in sites) {
      final type = site.siteType.toString().split('.').last;
      result[type] = (result[type] ?? 0) + 1;
    }
    return result;
  }

  Map<String, int> _getSitesByPeriod(List<CulturalSite> sites) {
    final Map<String, int> result = {};
    for (final site in sites) {
      final period = site.historicalPeriod.toString().split('.').last;
      result[period] = (result[period] ?? 0) + 1;
    }
    return result;
  }
}

/// Statistiky kulturních míst
class CulturalStatistics {
  final int totalSites;
  final int unescoSites;
  final int topRated;
  final int familyFriendly;
  final int historicallySignificant;
  final Map<String, int> byRegion;
  final Map<String, int> byType;
  final Map<String, int> byPeriod;
  final int totalEvents;
  final int currentEvents;

  CulturalStatistics({
    required this.totalSites,
    required this.unescoSites,
    required this.topRated,
    required this.familyFriendly,
    required this.historicallySignificant,
    required this.byRegion,
    required this.byType,
    required this.byPeriod,
    required this.totalEvents,
    required this.currentEvents,
  });
}
