import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/business.dart';

/// 🏢 BUSINESS SERVICE - B2B služby pro firmy
class BusinessService {
  static final BusinessService _instance = BusinessService._internal();
  factory BusinessService() => _instance;
  BusinessService._internal();

  bool _isInitialized = false;
  final List<BusinessProfile> _businesses = [];
  final List<BusinessBooking> _bookings = [];
  final Map<String, BusinessAnalytics> _analyticsCache = {};

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🏢 Inicializuji Business Service...');

      await _loadBusinesses();
      await _loadBookings();

      _isInitialized = true;
      debugPrint('✅ Business Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Business Service: $e');
      await _loadMockData();
      _isInitialized = true;
    }
  }

  /// Registrace nového podniku
  Future<BusinessProfile?> registerBusiness({
    required String name,
    required String description,
    required BusinessType type,
    required BusinessCategory category,
    required String address,
    required double latitude,
    required double longitude,
    required String phone,
    required String email,
    required String website,
    required BusinessOwner owner,
    required BusinessHours openingHours,
    List<String> amenities = const [],
    List<String> images = const [],
  }) async {
    try {
      final business = BusinessProfile(
        id: 'business_${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        type: type,
        category: category,
        address: address,
        latitude: latitude,
        longitude: longitude,
        phone: phone,
        email: email,
        website: website,
        images: images,
        openingHours: openingHours,
        amenities: amenities,
        rating: 0.0,
        reviewCount: 0,
        isVerified: false,
        isPremium: false,
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
        owner: owner,
      );

      _businesses.add(business);
      await _saveBusinesses();

      debugPrint('🏢 Podnik ${business.name} zaregistrován');
      return business;
    } catch (e) {
      debugPrint('❌ Chyba při registraci podniku: $e');
      return null;
    }
  }

  /// Aktualizace profilu podniku
  Future<bool> updateBusinessProfile(
    String businessId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final index = _businesses.indexWhere((b) => b.id == businessId);
      if (index == -1) return false;

      // Zde by byla implementace aktualizace
      // Pro demo pouze označíme jako aktualizováno
      debugPrint('🏢 Profil podniku $businessId aktualizován');
      await _saveBusinesses();
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při aktualizaci profilu: $e');
      return false;
    }
  }

  /// Získání analytics pro podnik
  Future<BusinessAnalytics> getBusinessAnalytics(
    String businessId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final cacheKey =
          '${businessId}_${startDate?.toIso8601String()}_${endDate?.toIso8601String()}';

      if (_analyticsCache.containsKey(cacheKey)) {
        return _analyticsCache[cacheKey]!;
      }

      // Simulace generování analytics
      final analytics = await _generateAnalytics(
        businessId,
        startDate,
        endDate,
      );
      _analyticsCache[cacheKey] = analytics;

      return analytics;
    } catch (e) {
      debugPrint('❌ Chyba při získávání analytics: $e');
      return _createFallbackAnalytics(businessId, startDate, endDate);
    }
  }

  /// Získání rezervací pro podnik
  Future<List<BusinessBooking>> getBusinessBookings(
    String businessId, {
    BookingStatus? status,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      var bookings = _bookings
          .where((b) => b.businessId == businessId)
          .toList();

      if (status != null) {
        bookings = bookings.where((b) => b.status == status).toList();
      }

      if (fromDate != null) {
        bookings = bookings
            .where((b) => b.serviceDate.isAfter(fromDate))
            .toList();
      }

      if (toDate != null) {
        bookings = bookings
            .where((b) => b.serviceDate.isBefore(toDate))
            .toList();
      }

      bookings.sort((a, b) => b.serviceDate.compareTo(a.serviceDate));
      return bookings;
    } catch (e) {
      debugPrint('❌ Chyba při získávání rezervací: $e');
      return [];
    }
  }

  /// Správa rezervace
  Future<bool> updateBookingStatus(
    String bookingId,
    BookingStatus newStatus,
  ) async {
    try {
      final index = _bookings.indexWhere((b) => b.id == bookingId);
      if (index == -1) return false;

      // Zde by byla implementace aktualizace stavu
      debugPrint('📅 Rezervace $bookingId změněna na ${newStatus.name}');
      await _saveBookings();
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při aktualizaci rezervace: $e');
      return false;
    }
  }

  /// Vytvoření nové rezervace
  Future<BusinessBooking?> createBooking({
    required String businessId,
    required String customerId,
    required String customerName,
    required String customerEmail,
    required String customerPhone,
    required DateTime serviceDate,
    required String serviceType,
    required int numberOfPeople,
    required double totalPrice,
    String? notes,
  }) async {
    try {
      final booking = BusinessBooking(
        id: 'booking_${DateTime.now().millisecondsSinceEpoch}',
        businessId: businessId,
        customerId: customerId,
        customerName: customerName,
        customerEmail: customerEmail,
        customerPhone: customerPhone,
        bookingDate: DateTime.now(),
        serviceDate: serviceDate,
        serviceType: serviceType,
        numberOfPeople: numberOfPeople,
        totalPrice: totalPrice,
        status: BookingStatus.pending,
        notes: notes,
        createdAt: DateTime.now(),
      );

      _bookings.add(booking);
      await _saveBookings();

      debugPrint('📅 Nová rezervace vytvořena: ${booking.id}');
      return booking;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření rezervace: $e');
      return null;
    }
  }

  /// Vyhledání podniků
  Future<List<BusinessProfile>> searchBusinesses({
    String? query,
    BusinessType? type,
    BusinessCategory? category,
    double? latitude,
    double? longitude,
    double radiusKm = 10.0,
    bool verifiedOnly = false,
    bool premiumOnly = false,
  }) async {
    try {
      var results = List<BusinessProfile>.from(_businesses);

      // Filtrování podle dotazu
      if (query != null && query.isNotEmpty) {
        results = results
            .where(
              (b) =>
                  b.name.toLowerCase().contains(query.toLowerCase()) ||
                  b.description.toLowerCase().contains(query.toLowerCase()),
            )
            .toList();
      }

      // Filtrování podle typu
      if (type != null) {
        results = results.where((b) => b.type == type).toList();
      }

      // Filtrování podle kategorie
      if (category != null) {
        results = results.where((b) => b.category == category).toList();
      }

      // Filtrování podle lokace
      if (latitude != null && longitude != null) {
        results = results.where((b) {
          final distance = _calculateDistance(
            latitude,
            longitude,
            b.latitude,
            b.longitude,
          );
          return distance <= radiusKm;
        }).toList();
      }

      // Filtrování podle verifikace
      if (verifiedOnly) {
        results = results.where((b) => b.isVerified).toList();
      }

      // Filtrování podle premium
      if (premiumOnly) {
        results = results.where((b) => b.isPremium).toList();
      }

      // Seřazení podle ratingu
      results.sort((a, b) => b.rating.compareTo(a.rating));

      return results;
    } catch (e) {
      debugPrint('❌ Chyba při vyhledávání podniků: $e');
      return [];
    }
  }

  /// Správa předplatného
  Future<bool> upgradeSubscription(
    String businessId,
    SubscriptionTier newTier,
  ) async {
    try {
      // Ověření existence podniku
      _businesses.firstWhere((b) => b.id == businessId);

      // Zde by byla implementace upgrade logiky
      debugPrint(
        '💳 Předplatné podniku $businessId upgradováno na ${newTier.name}',
      );
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při upgrade předplatného: $e');
      return false;
    }
  }

  /// Generování reportů
  Future<Map<String, dynamic>> generateBusinessReport(
    String businessId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final analytics = await getBusinessAnalytics(
        businessId,
        startDate: startDate,
        endDate: endDate,
      );
      final bookings = await getBusinessBookings(
        businessId,
        fromDate: startDate,
        toDate: endDate,
      );

      return {
        'period': {
          'start': startDate.toIso8601String(),
          'end': endDate.toIso8601String(),
        },
        'analytics': analytics.toJson(),
        'bookings': {
          'total': bookings.length,
          'confirmed': bookings
              .where((b) => b.status == BookingStatus.confirmed)
              .length,
          'cancelled': bookings
              .where((b) => b.status == BookingStatus.cancelled)
              .length,
          'revenue': bookings
              .where((b) => b.status == BookingStatus.completed)
              .fold(0.0, (sum, b) => sum + b.totalPrice),
        },
        'performance': {
          'clickThroughRate': analytics.clickThroughRate,
          'conversionRate': analytics.conversionRate,
          'averageBookingValue': bookings.isNotEmpty
              ? bookings.fold(0.0, (sum, b) => sum + b.totalPrice) /
                    bookings.length
              : 0.0,
        },
      };
    } catch (e) {
      debugPrint('❌ Chyba při generování reportu: $e');
      return {};
    }
  }

  /// Pomocné metody
  Future<BusinessAnalytics> _generateAnalytics(
    String businessId,
    DateTime? startDate,
    DateTime? endDate,
  ) async {
    final random = Random();
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    return BusinessAnalytics(
      businessId: businessId,
      periodStart: start,
      periodEnd: end,
      totalViews: 1000 + random.nextInt(5000),
      totalClicks: 100 + random.nextInt(500),
      totalBookings: 10 + random.nextInt(50),
      revenue: 5000 + random.nextDouble() * 20000,
      averageRating: 3.5 + random.nextDouble() * 1.5,
      newReviews: random.nextInt(20),
      trafficSources: {
        'Organic Search': 40 + random.nextInt(30),
        'Direct': 20 + random.nextInt(20),
        'Social Media': 15 + random.nextInt(15),
        'Referrals': 10 + random.nextInt(10),
      },
      popularTimes: {
        '9:00': random.nextInt(50),
        '12:00': random.nextInt(100),
        '15:00': random.nextInt(80),
        '18:00': random.nextInt(120),
        '21:00': random.nextInt(60),
      },
      topSearchKeywords: [
        'restaurant dubrovnik',
        'best food croatia',
        'local cuisine',
      ],
    );
  }

  BusinessAnalytics _createFallbackAnalytics(
    String businessId,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    final start =
        startDate ?? DateTime.now().subtract(const Duration(days: 30));
    final end = endDate ?? DateTime.now();

    return BusinessAnalytics(
      businessId: businessId,
      periodStart: start,
      periodEnd: end,
      totalViews: 0,
      totalClicks: 0,
      totalBookings: 0,
      revenue: 0.0,
      averageRating: 0.0,
      newReviews: 0,
    );
  }

  double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const double earthRadius = 6371; // km
    final double dLat = (lat2 - lat1) * (pi / 180);
    final double dLon = (lon2 - lon1) * (pi / 180);

    final double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(lat1 * (pi / 180)) *
            cos(lat2 * (pi / 180)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return earthRadius * c;
  }

  /// Načítání a ukládání dat
  Future<void> _loadBusinesses() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final businessesJson = prefs.getString('businesses');

      if (businessesJson != null) {
        final List<dynamic> data = jsonDecode(businessesJson);
        _businesses.clear();
        _businesses.addAll(
          data.map((json) => BusinessProfile.fromJson(json)).toList(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání podniků: $e');
    }
  }

  Future<void> _saveBusinesses() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'businesses',
        jsonEncode(_businesses.map((b) => b.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání podniků: $e');
    }
  }

  Future<void> _loadBookings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookingsJson = prefs.getString('business_bookings');

      if (bookingsJson != null) {
        final List<dynamic> data = jsonDecode(bookingsJson);
        _bookings.clear();
        _bookings.addAll(
          data.map((json) => BusinessBooking.fromJson(json)).toList(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při načítání rezervací: $e');
    }
  }

  Future<void> _saveBookings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'business_bookings',
        jsonEncode(_bookings.map((b) => b.toJson()).toList()),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání rezervací: $e');
    }
  }

  Future<void> _loadMockData() async {
    // Mock data pro demo
    final mockOwner = BusinessOwner(
      id: 'owner_1',
      firstName: 'Marko',
      lastName: 'Novak',
      email: '<EMAIL>',
      phone: '+385 91 123 4567',
      registeredAt: DateTime.now().subtract(const Duration(days: 365)),
    );

    final mockHours = BusinessHours(
      schedule: {
        'monday': const DayHours(
          isOpen: true,
          openTime: '09:00',
          closeTime: '22:00',
        ),
        'tuesday': const DayHours(
          isOpen: true,
          openTime: '09:00',
          closeTime: '22:00',
        ),
        'wednesday': const DayHours(
          isOpen: true,
          openTime: '09:00',
          closeTime: '22:00',
        ),
        'thursday': const DayHours(
          isOpen: true,
          openTime: '09:00',
          closeTime: '22:00',
        ),
        'friday': const DayHours(
          isOpen: true,
          openTime: '09:00',
          closeTime: '23:00',
        ),
        'saturday': const DayHours(
          isOpen: true,
          openTime: '10:00',
          closeTime: '23:00',
        ),
        'sunday': const DayHours(
          isOpen: true,
          openTime: '10:00',
          closeTime: '21:00',
        ),
      },
    );

    _businesses.add(
      BusinessProfile(
        id: 'business_1',
        name: 'Restaurant Dubrovnik',
        description: 'Tradiční chorvatská kuchyně v srdci Dubrovníku',
        type: BusinessType.restaurant,
        category: BusinessCategory.food,
        address: 'Stradun 1, Dubrovník',
        latitude: 42.641,
        longitude: 18.108,
        phone: '+385 20 123 456',
        email: '<EMAIL>',
        website: 'https://restaurant-dubrovnik.hr',
        openingHours: mockHours,
        amenities: ['WiFi', 'Terasa', 'Klimatizace', 'Platební karty'],
        rating: 4.5,
        reviewCount: 127,
        isVerified: true,
        isPremium: true,
        createdAt: DateTime.now().subtract(const Duration(days: 180)),
        lastUpdated: DateTime.now(),
        owner: mockOwner,
      ),
    );
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  List<BusinessProfile> get allBusinesses => List.unmodifiable(_businesses);
  List<BusinessBooking> get allBookings => List.unmodifiable(_bookings);
}
