import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class SmartCityScreen extends StatefulWidget {
  const SmartCityScreen({super.key});

  @override
  State<SmartCityScreen> createState() => _SmartCityScreenState();
}

class _SmartCityScreenState extends State<SmartCityScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Smart City stav
  String _selectedCity = 'Zagreb';

  // Statistiky
  int _availableParkingSpots = 245;
  int _activeSharedVehicles = 89;
  int _openServices = 12;
  int _reportedIssues = 7;
  int _wifiHotspots = 156;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _loadSmartCityData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSmartCityData() async {
    setState(() => _isLoading = true);

    // Simulace načtení dat
    await Future.delayed(const Duration(milliseconds: 800));

    setState(() {
      _availableParkingSpots = 245 + Random().nextInt(50);
      _activeSharedVehicles = 89 + Random().nextInt(20);
      _openServices = 12 + Random().nextInt(5);
      _reportedIssues = 7 + Random().nextInt(10);
      _wifiHotspots = 156 + Random().nextInt(30);
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Smart City $_selectedCity',
          style: GoogleFonts.playfairDisplay(
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF006994).withValues(alpha: 0.9),
                const Color(0xFF2E8B8B).withValues(alpha: 0.8),
                const Color(0xFF9C27B0).withValues(alpha: 0.7),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: WatercolorSmartCityHeaderPainter(),
            size: Size.infinite,
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: PopupMenuButton<String>(
              icon: const Icon(Icons.location_city),
              onSelected: (city) {
                setState(() {
                  _selectedCity = city;
                });
                _loadSmartCityData();
              },
              itemBuilder: (context) => [
                const PopupMenuItem(value: 'Zagreb', child: Text('Zagreb')),
                const PopupMenuItem(value: 'Split', child: Text('Split')),
                const PopupMenuItem(value: 'Rijeka', child: Text('Rijeka')),
                const PopupMenuItem(
                  value: 'Dubrovnik',
                  child: Text('Dubrovnik'),
                ),
                const PopupMenuItem(value: 'Zadar', child: Text('Zadar')),
                const PopupMenuItem(value: 'Pula', child: Text('Pula')),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: _loadSmartCityData,
              icon: const Icon(Icons.refresh),
              tooltip: 'Aktualizovat data',
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white.withValues(alpha: 0.3),
          ),
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Přehled'),
            Tab(icon: Icon(Icons.local_parking), text: 'Parkování'),
            Tab(icon: Icon(Icons.account_balance), text: 'Úřady'),
            Tab(icon: Icon(Icons.report_problem), text: 'Problémy'),
            Tab(icon: Icon(Icons.wifi), text: 'WiFi'),
            Tab(icon: Icon(Icons.directions_bike), text: 'Sdílení'),
          ],
        ),
      ),
      body: Container(
        child: CustomPaint(
          painter: WatercolorSmartCityBackgroundPainter(),
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildParkingTab(),
              _buildGovernmentTab(),
              _buildIssuesTab(),
              _buildWifiTab(),
              _buildSharingTab(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Hlavní statistiky
          Container(
            child: CustomPaint(
              painter: WatercolorSmartCityMainCardPainter(
                const Color(0xFF9C27B0),
              ),
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    Text(
                      'Smart City $_selectedCity',
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF9C27B0),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Přehled městských služeb',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Statistiky grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard(
                'Parkování',
                '$_availableParkingSpots',
                'volných míst',
                Icons.local_parking,
                const Color(0xFF006994),
              ),
              _buildStatCard(
                'Sdílení',
                '$_activeSharedVehicles',
                'vozidel',
                Icons.directions_bike,
                const Color(0xFF4CAF50),
              ),
              _buildStatCard(
                'Služby',
                '$_openServices',
                'otevřeno',
                Icons.account_balance,
                const Color(0xFF2E8B8B),
              ),
              _buildStatCard(
                'Problémy',
                '$_reportedIssues',
                'nahlášeno',
                Icons.report_problem,
                const Color(0xFFFF6B35),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // WiFi hotspoty
          _buildWifiOverviewCard(),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorSmartCityStatCardPainter(color),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorSmartCityIconPainter(color),
                  child: Icon(icon, size: 32, color: color),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: GoogleFonts.playfairDisplay(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: const Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF2C2C2C),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWifiOverviewCard() {
    return Container(
      child: CustomPaint(
        painter: WatercolorSmartCityWifiCardPainter(const Color(0xFF673AB7)),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorSmartCityIconPainter(
                    const Color(0xFF673AB7),
                  ),
                  child: Icon(
                    Icons.wifi,
                    size: 40,
                    color: const Color(0xFF673AB7),
                  ),
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'WiFi hotspoty',
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF673AB7),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '$_wifiHotspots aktivních hotspotů',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: const Color(0xFF673AB7),
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildParkingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildServiceHeader(
            'Parkování v městě',
            '$_availableParkingSpots volných míst',
            Icons.local_parking,
            const Color(0xFF006994),
          ),
          const SizedBox(height: 20),
          _buildParkingSpotCard(
            'Centrum - Trg bana Jelačića',
            '15 min',
            '12 HRK/h',
            8,
            20,
          ),
          _buildParkingSpotCard(
            'Kaptol - parkoviště',
            '5 min',
            '8 HRK/h',
            23,
            50,
          ),
          _buildParkingSpotCard(
            'Donji grad - Zrinjevac',
            '10 min',
            '10 HRK/h',
            5,
            30,
          ),
        ],
      ),
    );
  }

  Widget _buildGovernmentTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildServiceHeader(
            'Městské úřady',
            '$_openServices služeb otevřeno',
            Icons.account_balance,
            const Color(0xFF2E8B8B),
          ),
          const SizedBox(height: 20),
          _buildServiceCard(
            'Městský úřad',
            'Otevřeno do 16:00',
            Icons.business,
            true,
          ),
          _buildServiceCard(
            'Poliklinika',
            'Otevřeno do 20:00',
            Icons.local_hospital,
            true,
          ),
          _buildServiceCard('Knihovna', 'Zavřeno', Icons.library_books, false),
          _buildServiceCard('Pošta', 'Otevřeno do 18:00', Icons.mail, true),
        ],
      ),
    );
  }

  Widget _buildIssuesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildServiceHeader(
            'Hlášení problémů',
            '$_reportedIssues aktivních hlášení',
            Icons.report_problem,
            const Color(0xFFFF6B35),
          ),
          const SizedBox(height: 20),
          _buildIssueCard('Poškozená silnice', 'Ilica 15', 'Vysoká', 2),
          _buildIssueCard('Nefunkční osvětlení', 'Maksimirska 8', 'Střední', 5),
          _buildIssueCard('Odpadky', 'Trg kralja Tomislava', 'Nízká', 1),
        ],
      ),
    );
  }

  Widget _buildWifiTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildServiceHeader(
            'Veřejná WiFi',
            '$_wifiHotspots aktivních hotspotů',
            Icons.wifi,
            const Color(0xFF673AB7),
          ),
          const SizedBox(height: 20),
          _buildWifiCard('Zagreb_Free_WiFi', 'Centrum', 'Silný signál', true),
          _buildWifiCard('Zagreb_Public', 'Kaptol', 'Střední signál', true),
          _buildWifiCard('Zagreb_City', 'Donji grad', 'Slabý signál', false),
        ],
      ),
    );
  }

  Widget _buildSharingTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildServiceHeader(
            'Sdílená doprava',
            '$_activeSharedVehicles dostupných vozidel',
            Icons.directions_bike,
            const Color(0xFF4CAF50),
          ),
          const SizedBox(height: 20),
          _buildVehicleCard('Nextbike', 'Kolo', '2 min', '5 HRK/30min'),
          _buildVehicleCard('Bolt', 'Koloběžka', '1 min', '3 HRK/15min'),
          _buildVehicleCard('Uber', 'Auto', '5 min', '25 HRK/km'),
        ],
      ),
    );
  }

  Widget _buildServiceHeader(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      child: CustomPaint(
        painter: WatercolorSmartCityServiceHeaderPainter(color),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorSmartCityIconPainter(color),
                  child: Icon(icon, size: 32, color: color),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildParkingSpotCard(
    String name,
    String distance,
    String price,
    int available,
    int total,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: CustomPaint(
        painter: WatercolorSmartCityParkingCardPainter(const Color(0xFF006994)),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorSmartCityIconPainter(
                    const Color(0xFF006994),
                  ),
                  child: Icon(
                    Icons.local_parking,
                    size: 24,
                    color: const Color(0xFF006994),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF2C2C2C),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          '$available/$total volných',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: available > 5
                                ? const Color(0xFF4CAF50)
                                : const Color(0xFFFF6B35),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          price,
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  Text(
                    distance,
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF006994).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Rezervovat',
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF006994),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildServiceCard(
    String name,
    String status,
    IconData icon,
    bool isOpen,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: CustomPaint(
        painter: WatercolorSmartCityServiceCardPainter(const Color(0xFF2E8B8B)),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorSmartCityIconPainter(
                    const Color(0xFF2E8B8B),
                  ),
                  child: Icon(icon, size: 24, color: const Color(0xFF2E8B8B)),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF2C2C2C),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      status,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: isOpen
                            ? const Color(0xFF4CAF50)
                            : const Color(0xFFFF6B35),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: isOpen
                      ? const Color(0xFF4CAF50)
                      : const Color(0xFFFF6B35),
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIssueCard(
    String title,
    String location,
    String priority,
    int daysAgo,
  ) {
    Color priorityColor = priority == 'Vysoká'
        ? const Color(0xFFE53E3E)
        : priority == 'Střední'
        ? const Color(0xFFFF6B35)
        : const Color(0xFF4CAF50);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: CustomPaint(
        painter: WatercolorSmartCityIssueCardPainter(priorityColor),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorSmartCityIconPainter(priorityColor),
                  child: Icon(
                    Icons.report_problem,
                    size: 24,
                    color: priorityColor,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF2C2C2C),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      location,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: priorityColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      priority,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: priorityColor,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'před $daysAgo dny',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: const Color(0xFF999999),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWifiCard(
    String name,
    String location,
    String signal,
    bool isConnected,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: CustomPaint(
        painter: WatercolorSmartCityWifiCardPainter(const Color(0xFF673AB7)),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorSmartCityIconPainter(
                    const Color(0xFF673AB7),
                  ),
                  child: Icon(
                    Icons.wifi,
                    size: 24,
                    color: const Color(0xFF673AB7),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF2C2C2C),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          location,
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          signal,
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: signal.contains('Silný')
                                ? const Color(0xFF4CAF50)
                                : signal.contains('Střední')
                                ? const Color(0xFFFF6B35)
                                : const Color(0xFFE53E3E),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isConnected
                      ? const Color(0xFF4CAF50).withValues(alpha: 0.1)
                      : const Color(0xFF673AB7).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  isConnected ? 'Připojeno' : 'Připojit',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: isConnected
                        ? const Color(0xFF4CAF50)
                        : const Color(0xFF673AB7),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVehicleCard(
    String provider,
    String type,
    String distance,
    String price,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: CustomPaint(
        painter: WatercolorSmartCityVehicleCardPainter(const Color(0xFF4CAF50)),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Container(
                child: CustomPaint(
                  painter: WatercolorSmartCityIconPainter(
                    const Color(0xFF4CAF50),
                  ),
                  child: Icon(
                    type == 'Kolo'
                        ? Icons.directions_bike
                        : type == 'Koloběžka'
                        ? Icons.electric_scooter
                        : Icons.directions_car,
                    size: 24,
                    color: const Color(0xFF4CAF50),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '$provider - $type',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF2C2C2C),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          distance,
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF666666),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          price,
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: const Color(0xFF4CAF50),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Rezervovat',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF4CAF50),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Watercolor painters pro Smart City Screen
class WatercolorSmartCityHeaderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor vlny pro Smart City header
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.1,
      size.width * 0.5,
      size.height * 0.4,
    );
    path1.quadraticBezierTo(
      size.width * 0.75,
      size.height * 0.7,
      size.width,
      size.height * 0.2,
    );
    path1.lineTo(size.width, size.height);
    path1.lineTo(0, size.height);
    path1.close();

    paint.color = const Color(0xFF9C27B0).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhá vrstva
    final path2 = Path();
    path2.moveTo(0, size.height * 0.5);
    path2.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.7,
      size.height * 0.6,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.8,
      size.width,
      size.height * 0.4,
    );
    path2.lineTo(size.width, size.height);
    path2.lineTo(0, size.height);
    path2.close();

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.15);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSmartCityBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Jemný watercolor efekt pro pozadí smart city
    final path = Path();
    path.moveTo(size.width * 0.1, size.height * 0.05);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.02,
      size.width * 0.9,
      size.height * 0.08,
    );
    path.lineTo(size.width * 0.95, size.height * 0.95);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.98,
      size.width * 0.05,
      size.height * 0.92,
    );
    path.close();

    paint.color = const Color(0xFF9C27B0).withValues(alpha: 0.03);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSmartCityMainCardPainter extends CustomPainter {
  final Color color;

  WatercolorSmartCityMainCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro hlavní smart city kartu
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.02,
      size.width * 0.7,
      size.height * 0.08,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.15,
      size.width * 0.98,
      size.height * 0.9,
    );
    path.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.98,
      size.width * 0.3,
      size.height * 0.92,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.85,
      size.width * 0.05,
      size.height * 0.1,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);

    // Druhá vrstva pro hloubku
    final path2 = Path();
    path2.moveTo(size.width * 0.1, size.height * 0.2);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.05,
      size.width * 0.9,
      size.height * 0.15,
    );
    path2.lineTo(size.width * 0.85, size.height * 0.8);
    path2.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.95,
      size.width * 0.15,
      size.height * 0.85,
    );
    path2.close();

    paint.color = color.withValues(alpha: 0.05);
    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSmartCityStatCardPainter extends CustomPainter {
  final Color color;

  WatercolorSmartCityStatCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro stat karty
    final path = Path();
    path.moveTo(size.width * 0.08, size.height * 0.15);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.05,
      size.width * 0.8,
      size.height * 0.12,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.2,
      size.width * 0.92,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.05,
      size.height * 0.8,
      size.width * 0.08,
      size.height * 0.15,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.12);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSmartCityIconPainter extends CustomPainter {
  final Color color;

  WatercolorSmartCityIconPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor kruh kolem smart city ikony
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;

    final path = Path();
    for (int i = 0; i < 360; i += 20) {
      final angle = i * pi / 180;
      final variation = 0.8 + (sin(i * pi / 60) * 0.2);
      final x = center.dx + (radius * variation) * cos(angle);
      final y = center.dy + (radius * variation) * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    paint.color = color.withValues(alpha: 0.2);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSmartCityWifiCardPainter extends CustomPainter {
  final Color color;

  WatercolorSmartCityWifiCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro wifi karty
    final path = Path();
    path.moveTo(size.width * 0.03, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.05,
      size.width * 0.7,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.97,
      size.height * 0.25,
      size.width * 0.95,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.95,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.5,
      size.width * 0.03,
      size.height * 0.2,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSmartCityServiceHeaderPainter extends CustomPainter {
  final Color color;

  WatercolorSmartCityServiceHeaderPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro service header
    final path = Path();
    path.moveTo(size.width * 0.06, size.height * 0.12);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.03,
      size.width * 0.75,
      size.height * 0.09,
    );
    path.quadraticBezierTo(
      size.width * 0.94,
      size.height * 0.18,
      size.width * 0.97,
      size.height * 0.88,
    );
    path.quadraticBezierTo(
      size.width * 0.65,
      size.height * 0.97,
      size.width * 0.25,
      size.height * 0.91,
    );
    path.quadraticBezierTo(
      size.width * 0.03,
      size.height * 0.82,
      size.width * 0.06,
      size.height * 0.12,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.12);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSmartCityParkingCardPainter extends CustomPainter {
  final Color color;

  WatercolorSmartCityParkingCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro parking karty
    final path = Path();
    path.moveTo(size.width * 0.04, size.height * 0.18);
    path.quadraticBezierTo(
      size.width * 0.32,
      size.height * 0.06,
      size.width * 0.72,
      size.height * 0.14,
    );
    path.quadraticBezierTo(
      size.width * 0.96,
      size.height * 0.22,
      size.width * 0.94,
      size.height * 0.82,
    );
    path.quadraticBezierTo(
      size.width * 0.68,
      size.height * 0.94,
      size.width * 0.28,
      size.height * 0.86,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.78,
      size.width * 0.04,
      size.height * 0.18,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSmartCityServiceCardPainter extends CustomPainter {
  final Color color;

  WatercolorSmartCityServiceCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro service karty
    final path = Path();
    path.moveTo(size.width * 0.02, size.height * 0.25);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.08,
      size.width * 0.78,
      size.height * 0.18,
    );
    path.quadraticBezierTo(
      size.width * 0.98,
      size.height * 0.28,
      size.width * 0.96,
      size.height * 0.75,
    );
    path.quadraticBezierTo(
      size.width * 0.65,
      size.height * 0.92,
      size.width * 0.22,
      size.height * 0.82,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.55,
      size.width * 0.02,
      size.height * 0.25,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.09);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSmartCityIssueCardPainter extends CustomPainter {
  final Color color;

  WatercolorSmartCityIssueCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro issue karty
    final path = Path();
    path.moveTo(size.width * 0.03, size.height * 0.22);
    path.quadraticBezierTo(
      size.width * 0.38,
      size.height * 0.07,
      size.width * 0.82,
      size.height * 0.16,
    );
    path.quadraticBezierTo(
      size.width * 0.97,
      size.height * 0.25,
      size.width * 0.95,
      size.height * 0.78,
    );
    path.quadraticBezierTo(
      size.width * 0.62,
      size.height * 0.93,
      size.width * 0.18,
      size.height * 0.84,
    );
    path.quadraticBezierTo(
      size.width * 0.01,
      size.height * 0.52,
      size.width * 0.03,
      size.height * 0.22,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.1);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WatercolorSmartCityVehicleCardPainter extends CustomPainter {
  final Color color;

  WatercolorSmartCityVehicleCardPainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    // Watercolor okraje pro vehicle karty
    final path = Path();
    path.moveTo(size.width * 0.05, size.height * 0.2);
    path.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.06,
      size.width * 0.8,
      size.height * 0.15,
    );
    path.quadraticBezierTo(
      size.width * 0.95,
      size.height * 0.24,
      size.width * 0.93,
      size.height * 0.8,
    );
    path.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.94,
      size.width * 0.2,
      size.height * 0.85,
    );
    path.quadraticBezierTo(
      size.width * 0.02,
      size.height * 0.54,
      size.width * 0.05,
      size.height * 0.2,
    );
    path.close();

    paint.color = color.withValues(alpha: 0.08);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
