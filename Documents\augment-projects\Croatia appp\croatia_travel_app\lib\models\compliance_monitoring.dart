/// 🔒 COMPLIANCE MONITORING MODELS - Modely pro monitoring compliance
library;

/// Compliance framework enum
enum ComplianceFramework { gdpr, soc2, iso27001, pci, hipaa, custom }

/// Rule severity enum
enum RuleSeverity { info, low, medium, high, critical }

/// Compliance status enum
enum ComplianceStatus { compliant, nonCompliant, pending, unknown, inProgress }

/// Framework status enum
enum FrameworkStatus {
  compliant,
  partiallyCompliant,
  nonCompliant,
  notApplicable,
}

/// Compliance event type enum
enum ComplianceEventType {
  monitoringStarted,
  monitoringStopped,
  ruleAdded,
  ruleUpdated,
  ruleDeleted,
  violationDetected,
  violationResolved,
  checkCompleted,
  reportGenerated,
  criticalAlert,
  leadQualified,
  leadDisqualified,
  proposalCreated,
}

/// Violation status enum
enum ViolationStatus { open, inProgress, resolved, dismissed, escalated }

/// Compliance rule model
class ComplianceRule {
  final String id;
  final String name;
  final String description;
  final ComplianceFramework framework;
  final RuleSeverity severity;
  final Duration checkInterval;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastChecked;
  final Map<String, dynamic>? metadata;

  const ComplianceRule({
    required this.id,
    required this.name,
    required this.description,
    required this.framework,
    required this.severity,
    required this.checkInterval,
    this.isActive = true,
    required this.createdAt,
    this.lastChecked,
    this.metadata,
  });

  ComplianceRule copyWith({
    String? id,
    String? name,
    String? description,
    ComplianceFramework? framework,
    RuleSeverity? severity,
    Duration? checkInterval,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastChecked,
    Map<String, dynamic>? metadata,
  }) {
    return ComplianceRule(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      framework: framework ?? this.framework,
      severity: severity ?? this.severity,
      checkInterval: checkInterval ?? this.checkInterval,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastChecked: lastChecked ?? this.lastChecked,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'framework': framework.toString(),
    'severity': severity.toString(),
    'checkInterval': checkInterval.inMilliseconds,
    'isActive': isActive,
    'createdAt': createdAt.toIso8601String(),
    'lastChecked': lastChecked?.toIso8601String(),
    'metadata': metadata,
  };

  factory ComplianceRule.fromJson(Map<String, dynamic> json) => ComplianceRule(
    id: json['id'],
    name: json['name'],
    description: json['description'],
    framework: ComplianceFramework.values.firstWhere(
      (e) => e.toString() == json['framework'],
    ),
    severity: RuleSeverity.values.firstWhere(
      (e) => e.toString() == json['severity'],
    ),
    checkInterval: Duration(milliseconds: json['checkInterval']),
    isActive: json['isActive'] ?? true,
    createdAt: DateTime.parse(json['createdAt']),
    lastChecked: json['lastChecked'] != null
        ? DateTime.parse(json['lastChecked'])
        : null,
    metadata: json['metadata'],
  );
}

/// Compliance check result model
class ComplianceCheckResult {
  final String ruleId;
  final bool isCompliant;
  final String? message;
  final Map<String, dynamic>? details;
  final DateTime checkedAt;
  final String? evidence;
  final List<String>? recommendations;

  const ComplianceCheckResult({
    required this.ruleId,
    required this.isCompliant,
    this.message,
    this.details,
    required this.checkedAt,
    this.evidence,
    this.recommendations,
  });
}

/// Compliance violation model
class ComplianceViolation {
  final String id;
  final String ruleId;
  final String title;
  final String description;
  final RuleSeverity severity;
  final DateTime detectedAt;
  final DateTime? resolvedAt;
  final String? resolution;
  final Map<String, dynamic>? context;
  final bool isResolved;
  final ViolationStatus status;
  final ComplianceFramework framework;
  final String? ruleName;

  const ComplianceViolation({
    required this.id,
    required this.ruleId,
    required this.title,
    required this.description,
    required this.severity,
    required this.detectedAt,
    this.resolvedAt,
    this.resolution,
    this.context,
    this.isResolved = false,
    this.status = ViolationStatus.open,
    this.framework = ComplianceFramework.gdpr,
    this.ruleName,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'ruleId': ruleId,
    'title': title,
    'description': description,
    'severity': severity.toString(),
    'detectedAt': detectedAt.toIso8601String(),
    'resolvedAt': resolvedAt?.toIso8601String(),
    'resolution': resolution,
    'context': context,
    'isResolved': isResolved,
    'status': status.toString(),
    'framework': framework.toString(),
    'ruleName': ruleName,
  };

  factory ComplianceViolation.fromJson(Map<String, dynamic> json) =>
      ComplianceViolation(
        id: json['id'],
        ruleId: json['ruleId'],
        title: json['title'],
        description: json['description'],
        severity: RuleSeverity.values.firstWhere(
          (e) => e.toString() == json['severity'],
        ),
        detectedAt: DateTime.parse(json['detectedAt']),
        resolvedAt: json['resolvedAt'] != null
            ? DateTime.parse(json['resolvedAt'])
            : null,
        resolution: json['resolution'],
        context: json['context'],
        isResolved: json['isResolved'] ?? false,
        status: ViolationStatus.values.firstWhere(
          (e) => e.toString() == json['status'],
          orElse: () => ViolationStatus.open,
        ),
        framework: ComplianceFramework.values.firstWhere(
          (e) => e.toString() == json['framework'],
          orElse: () => ComplianceFramework.gdpr,
        ),
        ruleName: json['ruleName'],
      );
}

/// Compliance event model
class ComplianceEvent {
  final String id;
  final ComplianceEventType type;
  final String title;
  final String description;
  final RuleSeverity severity;
  final DateTime timestamp;
  final Map<String, dynamic>? data;
  final String? ruleId;
  final String? violationId;

  const ComplianceEvent({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.severity,
    required this.timestamp,
    this.data,
    this.ruleId,
    this.violationId,
  });
}

/// Compliance report model
class ComplianceReport {
  final String id;
  final DateTime generatedAt;
  final DateTime periodStart;
  final DateTime periodEnd;
  final int totalRules;
  final int compliantRules;
  final int nonCompliantRules;
  final int pendingRules;
  final double complianceScore;
  final List<ComplianceCheckResult> results;
  final List<ComplianceViolation> violations;
  final Map<ComplianceFramework, FrameworkStatus> frameworkStatus;
  final Map<String, dynamic>? summary;

  const ComplianceReport({
    required this.id,
    required this.generatedAt,
    required this.periodStart,
    required this.periodEnd,
    required this.totalRules,
    required this.compliantRules,
    required this.nonCompliantRules,
    required this.pendingRules,
    required this.complianceScore,
    required this.results,
    required this.violations,
    required this.frameworkStatus,
    this.summary,
  });
}

/// Compliance dashboard model
class ComplianceDashboard {
  final DateTime lastUpdated;
  final double overallScore;
  final int totalViolations;
  final int criticalViolations;
  final int resolvedViolations;
  final Map<ComplianceFramework, FrameworkStatus> frameworkStatus;
  final List<ComplianceEvent> recentEvents;
  final Map<String, dynamic> trends;
  final Map<String, int> violationsByCategory;

  const ComplianceDashboard({
    required this.lastUpdated,
    required this.overallScore,
    required this.totalViolations,
    required this.criticalViolations,
    required this.resolvedViolations,
    required this.frameworkStatus,
    required this.recentEvents,
    required this.trends,
    required this.violationsByCategory,
  });
}

/// Monitoring status model
class MonitoringStatus {
  final bool isActive;
  final List<String> activeMonitors;
  final DateTime? lastCheck;
  final int totalChecks;
  final int failedChecks;

  const MonitoringStatus({
    required this.isActive,
    required this.activeMonitors,
    this.lastCheck,
    this.totalChecks = 0,
    this.failedChecks = 0,
  });
}
