import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import 'package:geolocator/geolocator.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import '../models/camera_models.dart';

/// Služba pro camera a foto funkcionalita
class CameraService extends ChangeNotifier {
  static final CameraService _instance = CameraService._internal();
  factory CameraService() => _instance;
  CameraService._internal();

  final ImagePicker _imagePicker = ImagePicker();
  List<CameraDescription> _cameras = [];
  CameraController? _controller;
  CameraSettings _settings = const CameraSettings();
  List<TravelPhoto> _photos = [];
  bool _isInitialized = false;
  final bool _isRecording = false;

  // Gettery
  List<CameraDescription> get cameras => _cameras;
  CameraController? get controller => _controller;
  CameraSettings get settings => _settings;
  List<TravelPhoto> get photos => _photos;
  bool get isInitialized => _isInitialized;
  bool get isRecording => _isRecording;
  bool get hasCamera => _cameras.isNotEmpty;

  /// Inicializuje camera službu
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _cameras = await availableCameras();
      await _loadSettings();
      await _loadPhotos();

      if (_cameras.isNotEmpty) {
        await _initializeCamera(_cameras.first);
      }

      _isInitialized = true;
      debugPrint('Camera služba inicializována s ${_cameras.length} kamerami');
    } catch (e) {
      debugPrint('Chyba při inicializaci camera služby: $e');
      throw Exception('Nepodařilo se inicializovat kameru');
    }
  }

  /// Inicializuje konkrétní kameru
  Future<void> _initializeCamera(CameraDescription camera) async {
    try {
      _controller?.dispose();

      _controller = CameraController(
        camera,
        ResolutionPreset.high,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      await _controller!.initialize();
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při inicializaci kamery: $e');
      rethrow;
    }
  }

  /// Přepne na jinou kameru (přední/zadní)
  Future<void> switchCamera() async {
    if (_cameras.length < 2 || _controller == null) return;

    try {
      final currentCamera = _controller!.description;
      final newCamera = _cameras.firstWhere(
        (camera) => camera != currentCamera,
        orElse: () => _cameras.first,
      );

      await _initializeCamera(newCamera);
    } catch (e) {
      debugPrint('Chyba při přepínání kamery: $e');
    }
  }

  /// Pořídí fotografii
  Future<TravelPhoto?> takePicture() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      throw Exception('Kamera není inicializována');
    }

    try {
      final XFile image = await _controller!.takePicture();

      // Získá GPS souřadnice pokud je povoleno
      Position? position;
      if (_settings.isGeotaggingEnabled) {
        try {
          position = await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(
              accuracy: LocationAccuracy.high,
              timeLimit: Duration(seconds: 5),
            ),
          );
        } catch (e) {
          debugPrint('Nepodařilo se získat GPS pozici: $e');
        }
      }

      // Vytvoří TravelPhoto objekt
      final photo = TravelPhoto(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        filePath: image.path,
        latitude: position?.latitude,
        longitude: position?.longitude,
        takenAt: DateTime.now(),
        metadata: {
          'camera': _controller!.description.name,
          'resolution':
              '${_controller!.value.previewSize?.width}x${_controller!.value.previewSize?.height}',
          'flash': _settings.useFlash,
        },
      );

      // Uloží foto do galerie
      await _savePhoto(photo);

      debugPrint('Foto pořízeno: ${photo.id}');
      return photo;
    } catch (e) {
      debugPrint('Chyba při pořizování fotky: $e');
      rethrow;
    }
  }

  /// Vybere foto z galerie
  Future<TravelPhoto?> pickFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: _settings.photoQuality,
      );

      if (image == null) return null;

      // Vytvoří TravelPhoto objekt
      final photo = TravelPhoto(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        filePath: image.path,
        takenAt: DateTime.now(),
        metadata: {'source': 'gallery', 'original_path': image.path},
      );

      await _savePhoto(photo);
      return photo;
    } catch (e) {
      debugPrint('Chyba při výběru fotky z galerie: $e');
      rethrow;
    }
  }

  /// Uloží foto do aplikační složky
  Future<void> _savePhoto(TravelPhoto photo) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final photosDir = Directory(path.join(directory.path, 'photos'));

      if (!await photosDir.exists()) {
        await photosDir.create(recursive: true);
      }

      final fileName = '${photo.id}.jpg';
      final newPath = path.join(photosDir.path, fileName);

      // Zkopíruje soubor do aplikační složky
      final originalFile = File(photo.filePath);
      await originalFile.copy(newPath);

      // Aktualizuje cestu v objektu
      final updatedPhoto = photo.copyWith(filePath: newPath);

      _photos.add(updatedPhoto);
      await _savePhotosToStorage();
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při ukládání fotky: $e');
      rethrow;
    }
  }

  /// Smaže foto
  Future<void> deletePhoto(String photoId) async {
    try {
      final photoIndex = _photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) return;

      final photo = _photos[photoIndex];

      // Smaže soubor z disku
      final file = File(photo.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      // Odebere z seznamu
      _photos.removeAt(photoIndex);
      await _savePhotosToStorage();
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při mazání fotky: $e');
      rethrow;
    }
  }

  /// Označí foto jako oblíbené
  Future<void> toggleFavorite(String photoId) async {
    try {
      final photoIndex = _photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) return;

      _photos[photoIndex] = _photos[photoIndex].copyWith(
        isFavorite: !_photos[photoIndex].isFavorite,
      );

      await _savePhotosToStorage();
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při označování oblíbené fotky: $e');
    }
  }

  /// Přidá popis k fotce
  Future<void> updatePhotoDescription(
    String photoId,
    String? title,
    String? description,
  ) async {
    try {
      final photoIndex = _photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) return;

      _photos[photoIndex] = _photos[photoIndex].copyWith(
        title: title,
        description: description,
      );

      await _savePhotosToStorage();
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při aktualizaci popisu fotky: $e');
    }
  }

  /// Přidá tagy k fotce
  Future<void> addPhotoTags(String photoId, List<String> tags) async {
    try {
      final photoIndex = _photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) return;

      final currentTags = Set<String>.from(_photos[photoIndex].tags);
      currentTags.addAll(tags);

      _photos[photoIndex] = _photos[photoIndex].copyWith(
        tags: currentTags.toList(),
      );

      await _savePhotosToStorage();
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při přidávání tagů: $e');
    }
  }

  /// Získá fotky podle filtru
  List<TravelPhoto> getFilteredPhotos({
    bool? isFavorite,
    bool? hasLocation,
    List<String>? tags,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    return _photos.where((photo) {
      if (isFavorite != null && photo.isFavorite != isFavorite) return false;
      if (hasLocation != null && photo.hasLocation != hasLocation) return false;
      if (tags != null && tags.isNotEmpty) {
        if (!tags.any((tag) => photo.tags.contains(tag))) return false;
      }
      if (fromDate != null && photo.takenAt.isBefore(fromDate)) return false;
      if (toDate != null && photo.takenAt.isAfter(toDate)) return false;
      return true;
    }).toList();
  }

  /// Získá fotky z Chorvatska
  List<TravelPhoto> getCroatianPhotos() {
    return _photos.where((photo) => photo.isFromCroatia).toList();
  }

  /// Aktualizuje nastavení kamery
  Future<void> updateSettings(CameraSettings newSettings) async {
    try {
      _settings = newSettings;
      await _saveSettings();

      // Aplikuje nastavení na kameru
      if (_controller != null && _controller!.value.isInitialized) {
        await _controller!.setFlashMode(
          newSettings.useFlash ? FlashMode.auto : FlashMode.off,
        );
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při aktualizaci nastavení: $e');
    }
  }

  /// Načte nastavení z úložiště
  Future<void> _loadSettings() async {
    try {
      // TODO: Implementovat načítání z SharedPreferences
      _settings = const CameraSettings();
    } catch (e) {
      debugPrint('Chyba při načítání nastavení: $e');
    }
  }

  /// Uloží nastavení do úložiště
  Future<void> _saveSettings() async {
    try {
      // TODO: Implementovat ukládání do SharedPreferences
    } catch (e) {
      debugPrint('Chyba při ukládání nastavení: $e');
    }
  }

  /// Načte fotky z úložiště
  Future<void> _loadPhotos() async {
    try {
      // TODO: Implementovat načítání z SQLite databáze
      _photos = [];
    } catch (e) {
      debugPrint('Chyba při načítání fotek: $e');
    }
  }

  /// Uloží fotky do úložiště
  Future<void> _savePhotosToStorage() async {
    try {
      // TODO: Implementovat ukládání do SQLite databáze
    } catch (e) {
      debugPrint('Chyba při ukládání fotek: $e');
    }
  }

  /// Získá statistiky fotek
  Map<String, dynamic> getPhotoStats() {
    final totalPhotos = _photos.length;
    final favoritePhotos = _photos.where((p) => p.isFavorite).length;
    final geotaggedPhotos = _photos.where((p) => p.hasLocation).length;
    final croatianPhotos = _photos.where((p) => p.isFromCroatia).length;

    return {
      'total': totalPhotos,
      'favorites': favoritePhotos,
      'geotagged': geotaggedPhotos,
      'croatian': croatianPhotos,
      'uploaded': _photos.where((p) => p.isUploaded).length,
    };
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
}
