import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/accommodation.dart';

/// Služba pro objevování ubytování v Chorvatsku
/// Právně bezpečné řešení bez API závislostí
class AccommodationDiscoveryService {
  static final AccommodationDiscoveryService _instance =
      AccommodationDiscoveryService._internal();
  factory AccommodationDiscoveryService() => _instance;
  AccommodationDiscoveryService._internal();

  // Cache pro ubytování
  List<Accommodation>? _cachedAccommodations;
  DateTime? _lastCacheUpdate;
  final Duration _cacheValidDuration = const Duration(hours: 4);

  /// Získá všechna ubytování
  Future<List<Accommodation>> getAllAccommodations() async {
    if (_isCacheValid()) {
      return _cachedAccommodations!;
    }

    try {
      // Simulace načítání z lokální databáze
      await Future.delayed(const Duration(milliseconds: 700));

      _cachedAccommodations = _generateSampleAccommodations();
      _lastCacheUpdate = DateTime.now();

      return _cachedAccommodations!;
    } catch (e) {
      debugPrint('Chyba při načítání ubytování: $e');
      return [];
    }
  }

  /// Vyhledá ubytování podle kritérií
  Future<List<Accommodation>> searchAccommodations({
    String? query,
    String? region,
    AccommodationType? accommodationType,
    String? priceRange,
    bool? hasWifi,
    bool? hasParking,
    bool? hasPool,
    bool? hasSeaView,
    bool? hasPetPolicy,
    double? latitude,
    double? longitude,
    double? maxDistance,
    double? minRating,
  }) async {
    final allAccommodations = await getAllAccommodations();

    return allAccommodations.where((accommodation) {
      // Text search
      if (query != null && query.isNotEmpty) {
        final searchLower = query.toLowerCase();
        if (!accommodation.name.toLowerCase().contains(searchLower) &&
            !accommodation.description.toLowerCase().contains(searchLower) &&
            !accommodation.address.toLowerCase().contains(searchLower)) {
          return false;
        }
      }

      // Region filter
      if (region != null && region != 'all' && accommodation.region != region) {
        return false;
      }

      // Accommodation type filter
      if (accommodationType != null &&
          accommodation.accommodationType != accommodationType) {
        return false;
      }

      // Price range filter
      if (priceRange != null &&
          priceRange != 'all' &&
          accommodation.priceRange != priceRange) {
        return false;
      }

      // Amenity filters
      if (hasWifi == true && !accommodation.hasWifi) return false;
      if (hasParking == true && !accommodation.hasParking) return false;
      if (hasPool == true && !accommodation.hasPool) return false;
      if (hasSeaView == true && !accommodation.hasSeaView) return false;
      if (hasPetPolicy == true && !accommodation.hasPetPolicy) return false;

      // Distance filter
      if (latitude != null && longitude != null && maxDistance != null) {
        final distance = accommodation.distanceFrom(latitude, longitude);
        if (distance > maxDistance) {
          return false;
        }
      }

      // Rating filter
      if (minRating != null && accommodation.rating < minRating) {
        return false;
      }

      return true;
    }).toList();
  }

  /// Získá top hodnocená ubytování
  Future<List<Accommodation>> getTopRatedAccommodations({
    int limit = 20,
  }) async {
    final allAccommodations = await getAllAccommodations();

    final topRated = allAccommodations.where((a) => a.rating >= 4.5).toList();
    topRated.sort((a, b) => b.rating.compareTo(a.rating));

    return topRated.take(limit).toList();
  }

  /// Získá rodinná ubytování
  Future<List<Accommodation>> getFamilyFriendlyAccommodations({
    int limit = 20,
  }) async {
    final allAccommodations = await getAllAccommodations();

    final familyFriendly = allAccommodations
        .where((a) => a.isFamilyFriendly)
        .toList();
    familyFriendly.sort((a, b) => b.rating.compareTo(a.rating));

    return familyFriendly.take(limit).toList();
  }

  /// Získá luxusní ubytování
  Future<List<Accommodation>> getLuxuryAccommodations({int limit = 20}) async {
    final allAccommodations = await getAllAccommodations();

    final luxury = allAccommodations
        .where((a) => a.priceRange == 'luxury' || a.isLuxury)
        .toList();
    luxury.sort((a, b) => b.rating.compareTo(a.rating));

    return luxury.take(limit).toList();
  }

  /// Získá ubytování s výhledem na moře
  Future<List<Accommodation>> getSeaViewAccommodations({int limit = 20}) async {
    final allAccommodations = await getAllAccommodations();

    final seaView = allAccommodations.where((a) => a.hasSeaView).toList();
    seaView.sort((a, b) => b.rating.compareTo(a.rating));

    return seaView.take(limit).toList();
  }

  /// Získá ubytování v blízkosti
  Future<List<Accommodation>> getNearbyAccommodations(
    double latitude,
    double longitude, {
    double maxDistance = 15.0, // km
    int limit = 20,
  }) async {
    final allAccommodations = await getAllAccommodations();

    final nearby = allAccommodations
        .map((accommodation) {
          final distance = accommodation.distanceFrom(latitude, longitude);
          return MapEntry(accommodation, distance);
        })
        .where((entry) => entry.value <= maxDistance)
        .toList();

    nearby.sort((a, b) => a.value.compareTo(b.value));

    return nearby.take(limit).map((entry) => entry.key).toList();
  }

  /// Získá doporučená ubytování
  Future<List<Accommodation>> getRecommendedAccommodations({
    String? userRegion,
    List<String>? userPreferences,
    int limit = 10,
  }) async {
    final allAccommodations = await getAllAccommodations();

    // Seřadí podle hodnocení a popularity
    final recommended = allAccommodations
        .where((a) => a.rating >= 4.0)
        .toList();
    recommended.sort((a, b) {
      // Priorita: hodnocení * počet recenzí
      final scoreA = a.rating * (a.reviewCount / 50);
      final scoreB = b.rating * (b.reviewCount / 50);
      return scoreB.compareTo(scoreA);
    });

    return recommended.take(limit).toList();
  }

  /// Získá statistiky ubytování
  Future<AccommodationStatistics> getAccommodationStatistics() async {
    final allAccommodations = await getAllAccommodations();

    return AccommodationStatistics(
      totalAccommodations: allAccommodations.length,
      topRated: allAccommodations.where((a) => a.rating >= 4.5).length,
      familyFriendly: allAccommodations.where((a) => a.isFamilyFriendly).length,
      luxury: allAccommodations.where((a) => a.isLuxury).length,
      seaView: allAccommodations.where((a) => a.hasSeaView).length,
      byRegion: _getAccommodationsByRegion(allAccommodations),
      byType: _getAccommodationsByType(allAccommodations),
      byPriceRange: _getAccommodationsByPriceRange(allAccommodations),
    );
  }

  /// Kontroluje platnost cache
  bool _isCacheValid() {
    return _cachedAccommodations != null &&
        _lastCacheUpdate != null &&
        DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration;
  }

  /// Generuje ukázková data ubytování
  List<Accommodation> _generateSampleAccommodations() {
    final accommodations = <Accommodation>[];

    // Luxury hotels in Dubrovnik
    accommodations.addAll([
      _createAccommodation(
        'Hotel Excelsior Dubrovnik',
        'Luxusní hotel s výhledem na Staré město a Jadranské moře',
        'Frana Supila 12, Dubrovnik',
        42.6405,
        18.1094,
        'dubrovnik',
        AccommodationType.hotel,
        'luxury',
        4.8,
        234,
        hasWifi: true,
        hasParking: true,
        hasPool: true,
        hasRestaurant: true,
        hasSeaView: true,
        hasAirConditioning: true,
        '+385 20 353 353',
      ),
      _createAccommodation(
        'Villa Orsula',
        'Boutique vila s panoramatickým výhledem na moře',
        'Frana Supila 14, Dubrovnik',
        42.6398,
        18.1087,
        'dubrovnik',
        AccommodationType.villa,
        'luxury',
        4.9,
        89,
        hasWifi: true,
        hasParking: true,
        hasPool: true,
        hasRestaurant: true,
        hasSeaView: true,
        hasAirConditioning: true,
        '+385 20 441 555',
      ),
    ]);

    // Hotels in Split
    accommodations.addAll([
      _createAccommodation(
        'Hotel Park Split',
        'Moderní hotel v centru Splitu s výhledem na moře',
        'Hatzeov perivoj 3, Split',
        43.5081,
        16.4402,
        'dalmatia',
        AccommodationType.hotel,
        'upscale',
        4.6,
        178,
        hasWifi: true,
        hasParking: true,
        hasPool: false,
        hasRestaurant: true,
        hasSeaView: true,
        hasAirConditioning: true,
        '+385 21 406 400',
      ),
      _createAccommodation(
        'Villa Dalmacija',
        'Rodinná vila s bazénem a zahradou',
        'Poljička cesta 35, Split',
        43.5123,
        16.4567,
        'dalmatia',
        AccommodationType.villa,
        'mid',
        4.4,
        145,
        hasWifi: true,
        hasParking: true,
        hasPool: true,
        hasRestaurant: false,
        hasSeaView: false,
        hasAirConditioning: true,
        '+385 21 234 567',
      ),
    ]);

    // Istrian accommodations
    accommodations.addAll([
      _createAccommodation(
        'Hotel Lone Rovinj',
        'Designový hotel obklopený přírodním parkem',
        'Luje Adamovića 31, Rovinj',
        45.0678,
        13.6345,
        'istria',
        AccommodationType.resort,
        'luxury',
        4.7,
        203,
        hasWifi: true,
        hasParking: true,
        hasPool: true,
        hasRestaurant: true,
        hasSeaView: true,
        hasAirConditioning: true,
        '+385 52 632 000',
      ),
      _createAccommodation(
        'Apartmány Istria',
        'Moderní apartmány s kuchyňkou a terasou',
        'Istarska 25, Pula',
        44.8666,
        13.8496,
        'istria',
        AccommodationType.apartment,
        'mid',
        4.3,
        167,
        hasWifi: true,
        hasParking: true,
        hasPool: false,
        hasRestaurant: false,
        hasSeaView: false,
        hasAirConditioning: true,
        '+385 52 456 789',
      ),
    ]);

    // Kvarner accommodations
    accommodations.addAll([
      _createAccommodation(
        'Hotel Milenij',
        'Wellness hotel s spa centrem a výhledem na Kvarner',
        'Maršala Tita 109, Opatija',
        45.3371,
        14.3058,
        'kvarner',
        AccommodationType.hotel,
        'upscale',
        4.5,
        189,
        hasWifi: true,
        hasParking: true,
        hasPool: true,
        hasRestaurant: true,
        hasSeaView: true,
        hasAirConditioning: true,
        '+385 51 202 000',
      ),
      _createAccommodation(
        'Penzion Rab',
        'Rodinný penzion s domácí atmosférou',
        'Kampor 15, Rab',
        44.7567,
        14.7234,
        'kvarner',
        AccommodationType.guesthouse,
        'budget',
        4.2,
        98,
        hasWifi: true,
        hasParking: true,
        hasPool: false,
        hasRestaurant: false,
        hasSeaView: false,
        hasAirConditioning: false,
        '+385 51 776 123',
      ),
    ]);

    // Zagreb accommodations
    accommodations.addAll([
      _createAccommodation(
        'Hotel Esplanade Zagreb',
        'Historický luxusní hotel v centru Záhřebu',
        'Mihanovićeva 1, Zagreb',
        45.8131,
        15.9775,
        'zagreb',
        AccommodationType.boutique,
        'luxury',
        4.6,
        156,
        hasWifi: true,
        hasParking: true,
        hasPool: false,
        hasRestaurant: true,
        hasSeaView: false,
        hasAirConditioning: true,
        '+385 1 456 6666',
      ),
    ]);

    // Budget accommodations
    accommodations.addAll([
      _createAccommodation(
        'Hostel Villa Angelina',
        'Moderní hostel s společnými prostory',
        'Prilaz Gjure Deželića 23, Zagreb',
        45.8056,
        15.9661,
        'zagreb',
        AccommodationType.hostel,
        'budget',
        4.1,
        234,
        hasWifi: true,
        hasParking: false,
        hasPool: false,
        hasRestaurant: false,
        hasSeaView: false,
        hasAirConditioning: false,
        '+385 1 482 4294',
      ),
      _createAccommodation(
        'Kemp Solaris',
        'Rodinný kemp s mobilními domy a stanováním',
        'Solaris bb, Šibenik',
        43.7350,
        15.8952,
        'dalmatia',
        AccommodationType.camping,
        'budget',
        4.0,
        312,
        hasWifi: true,
        hasParking: true,
        hasPool: true,
        hasRestaurant: true,
        hasSeaView: false,
        hasAirConditioning: false,
        '+385 22 361 001',
      ),
    ]);

    // Přidá náhodná ubytování
    for (int i = 0; i < 8; i++) {
      accommodations.add(_createRandomAccommodation(Random()));
    }

    return accommodations;
  }

  Accommodation _createAccommodation(
    String name,
    String description,
    String address,
    double latitude,
    double longitude,
    String region,
    AccommodationType accommodationType,
    String priceRange,
    double rating,
    int reviewCount,
    String phone, {
    bool hasWifi = false,
    bool hasParking = false,
    bool hasPool = false,
    bool hasRestaurant = false,
    bool hasSeaView = false,
    bool hasAirConditioning = false,
    bool hasPetPolicy = false,
    bool hasAccessibility = false,
  }) {
    return Accommodation(
      id: 'acc_${name.toLowerCase().replaceAll(' ', '_')}',
      name: name,
      description: description,
      address: address,
      latitude: latitude,
      longitude: longitude,
      region: region,
      accommodationType: accommodationType,
      priceRange: priceRange,
      rating: rating,
      reviewCount: reviewCount,
      amenities: _generateAmenities(),
      phone: phone,
      website: 'https://www.${name.toLowerCase().replaceAll(' ', '')}.hr',
      hasWifi: hasWifi,
      hasParking: hasParking,
      hasPool: hasPool,
      hasRestaurant: hasRestaurant,
      hasAirConditioning: hasAirConditioning,
      hasSeaView: hasSeaView,
      hasPetPolicy: hasPetPolicy,
      hasAccessibility: hasAccessibility,
      roomCount: 20 + Random().nextInt(80),
      checkInTime: '14:00',
      checkOutTime: '10:00',
      photos: [],
      isActive: true,
      lastUpdated: DateTime.now(),
    );
  }

  Accommodation _createRandomAccommodation(Random random) {
    final regions = ['dalmatia', 'istria', 'kvarner', 'dubrovnik', 'zagreb'];
    final accommodationTypes = [
      AccommodationType.hotel,
      AccommodationType.apartment,
      AccommodationType.villa,
      AccommodationType.guesthouse,
    ];
    final priceRanges = ['budget', 'mid', 'upscale', 'luxury'];

    final names = [
      'Hotel Adriatic',
      'Villa Marina',
      'Apartmány Sunset',
      'Penzion Panorama',
      'Resort Dalmacija',
      'Boutique Central',
      'Hostel Modern',
      'Villa Paradise',
    ];

    final region = regions[random.nextInt(regions.length)];
    final name =
        '${names[random.nextInt(names.length)]} ${random.nextInt(100)}';

    return Accommodation(
      id: 'acc_random_${random.nextInt(10000)}',
      name: name,
      description:
          'Skvělé ubytování s výborným servisem a příjemnou atmosférou',
      address: 'Náhodná adresa ${random.nextInt(100)}, Chorvatsko',
      latitude: 42.0 + random.nextDouble() * 4.0,
      longitude: 13.0 + random.nextDouble() * 6.0,
      region: region,
      accommodationType:
          accommodationTypes[random.nextInt(accommodationTypes.length)],
      priceRange: priceRanges[random.nextInt(priceRanges.length)],
      rating: 3.5 + random.nextDouble() * 1.5,
      reviewCount: 20 + random.nextInt(200),
      amenities: _generateAmenities(),
      phone:
          '+385 ${random.nextInt(99)} ${random.nextInt(999)} ${random.nextInt(999)}',
      website: 'https://www.${name.toLowerCase().replaceAll(' ', '')}.hr',
      hasWifi: random.nextBool(),
      hasParking: random.nextBool(),
      hasPool: random.nextBool(),
      hasRestaurant: random.nextBool(),
      hasAirConditioning: random.nextBool(),
      hasSeaView: random.nextBool(),
      hasPetPolicy: random.nextBool(),
      hasAccessibility: random.nextBool(),
      roomCount: 10 + random.nextInt(90),
      checkInTime: '14:00',
      checkOutTime: '10:00',
      photos: [],
      isActive: true,
      lastUpdated: DateTime.now(),
    );
  }

  List<String> _generateAmenities() {
    return ['wifi', 'parking', 'breakfast'];
  }

  Map<String, int> _getAccommodationsByRegion(
    List<Accommodation> accommodations,
  ) {
    final Map<String, int> result = {};
    for (final accommodation in accommodations) {
      result[accommodation.region] = (result[accommodation.region] ?? 0) + 1;
    }
    return result;
  }

  Map<String, int> _getAccommodationsByType(
    List<Accommodation> accommodations,
  ) {
    final Map<String, int> result = {};
    for (final accommodation in accommodations) {
      final type = accommodation.accommodationType.toString().split('.').last;
      result[type] = (result[type] ?? 0) + 1;
    }
    return result;
  }

  Map<String, int> _getAccommodationsByPriceRange(
    List<Accommodation> accommodations,
  ) {
    final Map<String, int> result = {};
    for (final accommodation in accommodations) {
      result[accommodation.priceRange] =
          (result[accommodation.priceRange] ?? 0) + 1;
    }
    return result;
  }
}

/// Statistiky ubytování
class AccommodationStatistics {
  final int totalAccommodations;
  final int topRated;
  final int familyFriendly;
  final int luxury;
  final int seaView;
  final Map<String, int> byRegion;
  final Map<String, int> byType;
  final Map<String, int> byPriceRange;

  AccommodationStatistics({
    required this.totalAccommodations,
    required this.topRated,
    required this.familyFriendly,
    required this.luxury,
    required this.seaView,
    required this.byRegion,
    required this.byType,
    required this.byPriceRange,
  });
}
