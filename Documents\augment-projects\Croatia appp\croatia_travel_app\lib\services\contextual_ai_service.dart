import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/ai_orchestrator.dart';
import '../models/monument.dart';
import 'ai_orchestrator_service.dart';

/// Služba pro kontextové AI s porozuměním situaci
class ContextualAIService {
  static final ContextualAIService _instance = ContextualAIService._internal();
  factory ContextualAIService() => _instance;
  ContextualAIService._internal();

  bool _isInitialized = false;
  final Random _random = Random();

  // Znalostní báze pro kontextové odpovědi
  final Map<String, Map<String, List<String>>> _contextualKnowledge = {
    'location_based': {
      'dubrovnik': [
        'V Dubrovníku doporučuji procházku po hradbách města - nejlépe ráno nebo večer kvůli menšímu počtu turistů.',
        'Nezapomeňte navštívit Stradun, hlavn<PERSON> ulici Starého mě<PERSON>, a vyzkoušejte místní restaurace.',
        'Pro úžasný výhled vyjeďte lanovkou na horu <PERSON>đ, zejména při západu slunce.',
      ],
      'split': [
        'Split je ideální základna pro výlety na ostrovy Brač, Hvar nebo Vis.',
        'Diokleciánův palác je živoucí památka - lidé zde stále bydlí a pracují.',
        'Riva waterfront je perfektní místo pro večerní procházku a pozorování lidí.',
      ],
      'zagreb': [
        'Záhřeb má skvělou kávu - vyzkoušejte některou z kaváren v Tkalčićově ulici.',
        'Horní město (Gornji Grad) nabízí historické památky a úžasný výhled na město.',
        'Dolac Market je nejlepší místo pro nákup čerstvých místních produktů.',
      ],
    },
    'time_based': {
      'morning': [
        'Ráno je ideální čas pro návštěvu památek - méně turistů a příjemnější teploty.',
        'Doporučuji začít den návštěvou místního trhu pro čerstvé ovoce a kávu.',
        'Ranní procházka po pobřeží je skvělý způsob, jak začít den v Chorvatsku.',
      ],
      'afternoon': [
        'Odpoledne je perfektní čas pro pláž nebo relaxaci u moře.',
        'Pokud je horko, navštivte muzea nebo galerie s klimatizací.',
        'Odpolední siesta je v Chorvatsku běžná - mnoho obchodů má zavřeno.',
      ],
      'evening': [
        'Večer je nejlepší čas pro večeři - Chorvaté jedí pozdě, kolem 20-21 hodin.',
        'Sunset je v Chorvatsku nádherný - najděte si místo s výhledem na západ.',
        'Večerní procházka po starých městech má zvláštní atmosféru.',
      ],
    },
    'weather_based': {
      'sunny': [
        'Při slunečném počasí využijte čas na pláži nebo vodní sporty.',
        'Nezapomeňte na opalovací krém - chorvatské slunce je silné.',
        'Ideální počasí pro výlet lodí na ostrovy.',
      ],
      'rainy': [
        'Při dešti navštivte muzea, galerie nebo místní kavárny.',
        'Deštivý den je perfektní pro ochutnávku místních specialit v restauracích.',
        'Chorvatské kostely a katedrály nabízejí úkryt i kulturní zážitek.',
      ],
    },
    'budget_based': {
      'low': [
        'Vyzkoušejte místní pekárny a tržiště pro levné a chutné jídlo.',
        'Mnoho pláží je veřejných a zdarma.',
        'Procházky po historických centrech nic nestojí a jsou nádherné.',
      ],
      'medium': [
        'Kombinujte návštěvy placených památek s bezplatnými aktivitami.',
        'Místní restaurace mimo turistické centrum nabízejí lepší poměr cena/kvalita.',
        'Zvažte nákup city pass pro slevy na památky a dopravu.',
      ],
      'high': [
        'Dopřejte si luxusní restaurace s výhledem na moře.',
        'Soukromé výlety lodí nebo s průvodcem stojí za to.',
        'Wellness hotely v Chorvatsku nabízejí skvělé služby.',
      ],
    },
  };

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🧠 Inicializuji Contextual AI Service...');
      _isInitialized = true;
      debugPrint('✅ Contextual AI Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Contextual AI: $e');
      _isInitialized = true;
    }
  }

  /// Analýza dotazu s kontextem
  Future<QueryAnalysis> analyzeQuery(String query, AIContext context) async {
    final lowerQuery = query.toLowerCase();

    // Detekce záměru
    QueryIntent intent = _detectIntent(lowerQuery);

    // Výpočet složitosti
    double complexity = _calculateComplexity(lowerQuery);

    // Extrakce entit
    List<String> entities = _extractEntities(lowerQuery);

    return QueryAnalysis(
      intent: intent,
      complexity: complexity,
      entities: entities,
      metadata: {
        'has_location': _hasLocationReference(lowerQuery),
        'has_time': _hasTimeReference(lowerQuery),
        'has_preference': _hasPreferenceReference(lowerQuery),
      },
    );
  }

  /// Generování kontextové odpovědi
  Future<AIResponse> generateResponse(String query, AIContext context) async {
    try {
      final analysis = await analyzeQuery(query, context);

      // Sestavení kontextové odpovědi
      String response = await _buildContextualResponse(
        query,
        context,
        analysis,
      );

      // Generování doporučení
      List<AIRecommendation> recommendations = await _generateRecommendations(
        query,
        context,
      );

      return AIResponse(
        content: response,
        type: _getResponseType(analysis),
        confidence: _calculateConfidence(analysis, context),
        source: AIResponseSource.local,
        recommendations: recommendations,
        timestamp: DateTime.now(),
        metadata: {
          'context_used': true,
          'location_aware': context.currentLocation != null,
          'personalized': context.userProfile != null,
        },
      );
    } catch (e) {
      debugPrint('❌ Chyba při generování kontextové odpovědi: $e');
      return _createErrorResponse();
    }
  }

  /// Generování odpovědi pro rozpoznanou památku
  Future<String> generateMonumentResponse(Monument monument) async {
    final responses = [
      'Rozpoznal jsem ${monument.name}! ${monument.description}',
      'To je ${monument.name} - ${monument.description}',
      'Skvělé! Nacházíte se u ${monument.name}. ${monument.description}',
    ];

    return responses[_random.nextInt(responses.length)];
  }

  /// Personalizovaná doporučení
  Future<List<AIRecommendation>> getPersonalizedRecommendations(
    UserProfile profile,
    LocationData location, {
    int limit = 10,
  }) async {
    final recommendations = <AIRecommendation>[];

    // Doporučení na základě zájmů
    for (final interest in profile.interests) {
      final recs = await _getRecommendationsByInterest(interest, location);
      recommendations.addAll(recs);
    }

    // Doporučení na základě stylu cestování
    final styleRecs = await _getRecommendationsByTravelStyle(
      profile.travelStyle,
      location,
    );
    recommendations.addAll(styleRecs);

    // Doporučení na základě rozpočtu
    final budgetRecs = await _getRecommendationsByBudget(
      profile.budget,
      location,
    );
    recommendations.addAll(budgetRecs);

    // Seřazení podle skóre a omezení
    recommendations.sort((a, b) => b.score.compareTo(a.score));
    return recommendations.take(limit).toList();
  }

  /// Detekce záměru dotazu
  QueryIntent _detectIntent(String query) {
    if (_containsAny(query, ['kde', 'jak se dostanu', 'cesta', 'navigace'])) {
      return QueryIntent.navigation;
    }
    if (_containsAny(query, ['doporuč', 'najdi', 'hledám', 'chci'])) {
      return QueryIntent.recommendation;
    }
    if (_containsAny(query, ['co je', 'kdo je', 'kdy', 'proč', 'jak'])) {
      return QueryIntent.factual;
    }
    if (query.length > 100 ||
        _containsAny(query, ['naplánuj', 'vytvoř', 'napiš'])) {
      return QueryIntent.complex;
    }
    return QueryIntent.simple;
  }

  /// Výpočet složitosti dotazu
  double _calculateComplexity(String query) {
    double complexity = 0.0;

    // Délka dotazu
    complexity += query.length / 200.0;

    // Počet otázek
    complexity += query.split('?').length * 0.2;

    // Složitá klíčová slova
    final complexWords = ['naplánuj', 'porovnej', 'analyzuj', 'vysvětli'];
    for (final word in complexWords) {
      if (query.contains(word)) complexity += 0.3;
    }

    return complexity.clamp(0.0, 1.0);
  }

  /// Extrakce entit z dotazu
  List<String> _extractEntities(String query) {
    final entities = <String>[];

    // Města
    final cities = ['dubrovník', 'split', 'záhřeb', 'pula', 'rijeka', 'zadar'];
    for (final city in cities) {
      if (query.contains(city)) entities.add(city);
    }

    // Typy míst
    final placeTypes = ['restaurace', 'hotel', 'pláž', 'muzeum', 'kostel'];
    for (final type in placeTypes) {
      if (query.contains(type)) entities.add(type);
    }

    return entities;
  }

  /// Sestavení kontextové odpovědi
  Future<String> _buildContextualResponse(
    String query,
    AIContext context,
    QueryAnalysis analysis,
  ) async {
    final buffer = StringBuffer();

    // Základní odpověď
    String baseResponse = await _getBaseResponse(query, analysis);
    buffer.writeln(baseResponse);

    // Kontextové doplnění
    if (context.currentLocation != null) {
      final locationContext = _getLocationContext(context.currentLocation!);
      if (locationContext.isNotEmpty) {
        buffer.writeln('\n💡 $locationContext');
      }
    }

    // Časový kontext
    final timeContext = _getTimeContext();
    if (timeContext.isNotEmpty) {
      buffer.writeln('\n⏰ $timeContext');
    }

    // Personalizace
    if (context.userProfile != null) {
      final personalContext = _getPersonalContext(context.userProfile!);
      if (personalContext.isNotEmpty) {
        buffer.writeln('\n🎯 $personalContext');
      }
    }

    return buffer.toString().trim();
  }

  /// Získání základní odpovědi
  Future<String> _getBaseResponse(String query, QueryAnalysis analysis) async {
    // Zde by byla implementace základní logiky odpovědí
    return 'Rád vám pomohu s vaším dotazem o Chorvatsku.';
  }

  /// Získání lokačního kontextu
  String _getLocationContext(LocationData location) {
    final city = location.city?.toLowerCase();
    if (city != null &&
        _contextualKnowledge['location_based']!.containsKey(city)) {
      final tips = _contextualKnowledge['location_based']![city]!;
      return tips[_random.nextInt(tips.length)];
    }
    return '';
  }

  /// Získání časového kontextu
  String _getTimeContext() {
    final hour = DateTime.now().hour;
    String timeOfDay;

    if (hour < 12) {
      timeOfDay = 'morning';
    } else if (hour < 18) {
      timeOfDay = 'afternoon';
    } else {
      timeOfDay = 'evening';
    }

    final tips = _contextualKnowledge['time_based']![timeOfDay]!;
    return tips[_random.nextInt(tips.length)];
  }

  /// Získání osobního kontextu
  String _getPersonalContext(UserProfile profile) {
    final budget = profile.budget.name;
    if (_contextualKnowledge['budget_based']!.containsKey(budget)) {
      final tips = _contextualKnowledge['budget_based']![budget]!;
      return tips[_random.nextInt(tips.length)];
    }
    return '';
  }

  /// Generování doporučení
  Future<List<AIRecommendation>> _generateRecommendations(
    String query,
    AIContext context,
  ) async {
    // Implementace generování doporučení na základě dotazu a kontextu
    return [];
  }

  /// Doporučení podle zájmů
  Future<List<AIRecommendation>> _getRecommendationsByInterest(
    String interest,
    LocationData location,
  ) async {
    // Implementace doporučení podle zájmů
    return [];
  }

  /// Doporučení podle stylu cestování
  Future<List<AIRecommendation>> _getRecommendationsByTravelStyle(
    TravelStyle style,
    LocationData location,
  ) async {
    // Implementace doporučení podle stylu
    return [];
  }

  /// Doporučení podle rozpočtu
  Future<List<AIRecommendation>> _getRecommendationsByBudget(
    Budget budget,
    LocationData location,
  ) async {
    // Implementace doporučení podle rozpočtu
    return [];
  }

  /// Pomocné metody
  bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }

  bool _hasLocationReference(String query) {
    return _containsAny(query, ['kde', 'v', 'na', 'do', 'z']);
  }

  bool _hasTimeReference(String query) {
    return _containsAny(query, ['kdy', 'ráno', 'večer', 'dnes', 'zítra']);
  }

  bool _hasPreferenceReference(String query) {
    return _containsAny(query, ['rád', 'nerad', 'preferuji', 'chci', 'nechci']);
  }

  AIResponseType _getResponseType(QueryAnalysis analysis) {
    switch (analysis.intent) {
      case QueryIntent.recommendation:
        return AIResponseType.recommendation;
      case QueryIntent.navigation:
        return AIResponseType.action;
      default:
        return AIResponseType.text;
    }
  }

  double _calculateConfidence(QueryAnalysis analysis, AIContext context) {
    double confidence = 0.7; // Základní confidence

    // Bonus za kontext
    if (context.currentLocation != null) confidence += 0.1;
    if (context.userProfile != null) confidence += 0.1;
    if (context.memory != null) confidence += 0.05;

    // Malus za složitost
    confidence -= analysis.complexity * 0.2;

    return confidence.clamp(0.0, 1.0);
  }

  AIResponse _createErrorResponse() {
    return AIResponse(
      content: 'Omlouvám se, došlo k chybě při zpracování vašeho dotazu.',
      type: AIResponseType.error,
      confidence: 0.0,
      source: AIResponseSource.local,
      timestamp: DateTime.now(),
    );
  }

  // Gettery
  bool get isInitialized => _isInitialized;
}
