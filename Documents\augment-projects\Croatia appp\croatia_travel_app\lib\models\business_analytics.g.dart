// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business_analytics.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AnalyticsDashboard _$AnalyticsDashboardFromJson(Map<String, dynamic> json) =>
    AnalyticsDashboard(
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      totalRevenue: (json['totalRevenue'] as num).toDouble(),
      totalUsers: (json['totalUsers'] as num).toInt(),
      activeUsers: (json['activeUsers'] as num).toInt(),
      conversionRate: (json['conversionRate'] as num).toDouble(),
      retentionRate: (json['retentionRate'] as num).toDouble(),
      revenueByCategory:
          (json['revenueByCategory'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      usersByRegion: Map<String, int>.from(json['usersByRegion'] as Map),
      recentEvents: (json['recentEvents'] as List<dynamic>)
          .map((e) => AnalyticsEvent.fromJson(e as Map<String, dynamic>))
          .toList(),
      trends: json['trends'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$AnalyticsDashboardToJson(AnalyticsDashboard instance) =>
    <String, dynamic>{
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'totalRevenue': instance.totalRevenue,
      'totalUsers': instance.totalUsers,
      'activeUsers': instance.activeUsers,
      'conversionRate': instance.conversionRate,
      'retentionRate': instance.retentionRate,
      'revenueByCategory': instance.revenueByCategory,
      'usersByRegion': instance.usersByRegion,
      'recentEvents': instance.recentEvents,
      'trends': instance.trends,
    };

AnalyticsEvent _$AnalyticsEventFromJson(Map<String, dynamic> json) =>
    AnalyticsEvent(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      properties: json['properties'] as Map<String, dynamic>,
      timestamp: DateTime.parse(json['timestamp'] as String),
      userId: json['userId'] as String?,
      sessionId: json['sessionId'] as String?,
    );

Map<String, dynamic> _$AnalyticsEventToJson(AnalyticsEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'category': instance.category,
      'properties': instance.properties,
      'timestamp': instance.timestamp.toIso8601String(),
      'userId': instance.userId,
      'sessionId': instance.sessionId,
    };

AnalyticsReport _$AnalyticsReportFromJson(Map<String, dynamic> json) =>
    AnalyticsReport(
      id: json['id'] as String,
      type: $enumDecode(_$ReportTypeEnumMap, json['type']),
      fromDate: DateTime.parse(json['fromDate'] as String),
      toDate: DateTime.parse(json['toDate'] as String),
      metrics: (json['metrics'] as List<dynamic>)
          .map((e) => $enumDecode(_$MetricTypeEnumMap, e))
          .toList(),
      dashboard: AnalyticsDashboard.fromJson(
          json['dashboard'] as Map<String, dynamic>),
      insights:
          (json['insights'] as List<dynamic>).map((e) => e as String).toList(),
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      generatedAt: DateTime.parse(json['generatedAt'] as String),
      exportFormat:
          $enumDecodeNullable(_$ExportFormatEnumMap, json['exportFormat']),
    );

Map<String, dynamic> _$AnalyticsReportToJson(AnalyticsReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$ReportTypeEnumMap[instance.type]!,
      'fromDate': instance.fromDate.toIso8601String(),
      'toDate': instance.toDate.toIso8601String(),
      'metrics': instance.metrics.map((e) => _$MetricTypeEnumMap[e]!).toList(),
      'dashboard': instance.dashboard,
      'insights': instance.insights,
      'recommendations': instance.recommendations,
      'generatedAt': instance.generatedAt.toIso8601String(),
      'exportFormat': _$ExportFormatEnumMap[instance.exportFormat],
    };

const _$ReportTypeEnumMap = {
  ReportType.daily: 'daily',
  ReportType.weekly: 'weekly',
  ReportType.monthly: 'monthly',
  ReportType.quarterly: 'quarterly',
  ReportType.yearly: 'yearly',
  ReportType.custom: 'custom',
};

const _$MetricTypeEnumMap = {
  MetricType.revenue: 'revenue',
  MetricType.users: 'users',
  MetricType.sessions: 'sessions',
  MetricType.conversion: 'conversion',
  MetricType.retention: 'retention',
  MetricType.engagement: 'engagement',
};

const _$ExportFormatEnumMap = {
  ExportFormat.pdf: 'pdf',
  ExportFormat.excel: 'excel',
  ExportFormat.csv: 'csv',
  ExportFormat.json: 'json',
};

UserAnalytics _$UserAnalyticsFromJson(Map<String, dynamic> json) =>
    UserAnalytics(
      userId: json['userId'] as String,
      totalSessions: (json['totalSessions'] as num).toInt(),
      totalTime: Duration(microseconds: (json['totalTime'] as num).toInt()),
      firstVisit: DateTime.parse(json['firstVisit'] as String),
      lastVisit: DateTime.parse(json['lastVisit'] as String),
      visitedPages: (json['visitedPages'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      actionCounts: Map<String, int>.from(json['actionCounts'] as Map),
      engagementScore: (json['engagementScore'] as num).toDouble(),
    );

Map<String, dynamic> _$UserAnalyticsToJson(UserAnalytics instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'totalSessions': instance.totalSessions,
      'totalTime': instance.totalTime.inMicroseconds,
      'firstVisit': instance.firstVisit.toIso8601String(),
      'lastVisit': instance.lastVisit.toIso8601String(),
      'visitedPages': instance.visitedPages,
      'actionCounts': instance.actionCounts,
      'engagementScore': instance.engagementScore,
    };

RevenueAnalytics _$RevenueAnalyticsFromJson(Map<String, dynamic> json) =>
    RevenueAnalytics(
      totalRevenue: (json['totalRevenue'] as num).toDouble(),
      monthlyRecurringRevenue:
          (json['monthlyRecurringRevenue'] as num).toDouble(),
      averageOrderValue: (json['averageOrderValue'] as num).toDouble(),
      totalOrders: (json['totalOrders'] as num).toInt(),
      revenueByProduct: (json['revenueByProduct'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      revenueByRegion: (json['revenueByRegion'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      monthlyMetrics: (json['monthlyMetrics'] as List<dynamic>)
          .map((e) => RevenueMetric.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$RevenueAnalyticsToJson(RevenueAnalytics instance) =>
    <String, dynamic>{
      'totalRevenue': instance.totalRevenue,
      'monthlyRecurringRevenue': instance.monthlyRecurringRevenue,
      'averageOrderValue': instance.averageOrderValue,
      'totalOrders': instance.totalOrders,
      'revenueByProduct': instance.revenueByProduct,
      'revenueByRegion': instance.revenueByRegion,
      'monthlyMetrics': instance.monthlyMetrics,
    };

RevenueMetric _$RevenueMetricFromJson(Map<String, dynamic> json) =>
    RevenueMetric(
      month: DateTime.parse(json['month'] as String),
      revenue: (json['revenue'] as num).toDouble(),
      orders: (json['orders'] as num).toInt(),
      averageOrderValue: (json['averageOrderValue'] as num).toDouble(),
      growthRate: (json['growthRate'] as num).toDouble(),
    );

Map<String, dynamic> _$RevenueMetricToJson(RevenueMetric instance) =>
    <String, dynamic>{
      'month': instance.month.toIso8601String(),
      'revenue': instance.revenue,
      'orders': instance.orders,
      'averageOrderValue': instance.averageOrderValue,
      'growthRate': instance.growthRate,
    };

ConversionFunnel _$ConversionFunnelFromJson(Map<String, dynamic> json) =>
    ConversionFunnel(
      name: json['name'] as String,
      steps: (json['steps'] as List<dynamic>)
          .map((e) => FunnelStep.fromJson(e as Map<String, dynamic>))
          .toList(),
      overallConversionRate: (json['overallConversionRate'] as num).toDouble(),
      analyzedAt: DateTime.parse(json['analyzedAt'] as String),
    );

Map<String, dynamic> _$ConversionFunnelToJson(ConversionFunnel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'steps': instance.steps,
      'overallConversionRate': instance.overallConversionRate,
      'analyzedAt': instance.analyzedAt.toIso8601String(),
    };

FunnelStep _$FunnelStepFromJson(Map<String, dynamic> json) => FunnelStep(
      name: json['name'] as String,
      users: (json['users'] as num).toInt(),
      conversionRate: (json['conversionRate'] as num).toDouble(),
      dropOffRate: (json['dropOffRate'] as num).toDouble(),
    );

Map<String, dynamic> _$FunnelStepToJson(FunnelStep instance) =>
    <String, dynamic>{
      'name': instance.name,
      'users': instance.users,
      'conversionRate': instance.conversionRate,
      'dropOffRate': instance.dropOffRate,
    };

CohortAnalysis _$CohortAnalysisFromJson(Map<String, dynamic> json) =>
    CohortAnalysis(
      cohortType: json['cohortType'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      cohorts: (json['cohorts'] as List<dynamic>)
          .map((e) => CohortData.fromJson(e as Map<String, dynamic>))
          .toList(),
      retentionRates: (json['retentionRates'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$CohortAnalysisToJson(CohortAnalysis instance) =>
    <String, dynamic>{
      'cohortType': instance.cohortType,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'cohorts': instance.cohorts,
      'retentionRates': instance.retentionRates,
    };

CohortData _$CohortDataFromJson(Map<String, dynamic> json) => CohortData(
      cohortDate: DateTime.parse(json['cohortDate'] as String),
      initialUsers: (json['initialUsers'] as num).toInt(),
      retentionByPeriod:
          (json['retentionByPeriod'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(int.parse(k), (e as num).toInt()),
      ),
      retentionRates: (json['retentionRates'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(int.parse(k), (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$CohortDataToJson(CohortData instance) =>
    <String, dynamic>{
      'cohortDate': instance.cohortDate.toIso8601String(),
      'initialUsers': instance.initialUsers,
      'retentionByPeriod':
          instance.retentionByPeriod.map((k, e) => MapEntry(k.toString(), e)),
      'retentionRates':
          instance.retentionRates.map((k, e) => MapEntry(k.toString(), e)),
    };

BusinessClient _$BusinessClientFromJson(Map<String, dynamic> json) =>
    BusinessClient(
      id: json['id'] as String,
      companyName: json['companyName'] as String,
      industry: json['industry'] as String,
      contactEmail: json['contactEmail'] as String,
      contactPhone: json['contactPhone'] as String,
      onboardedAt: DateTime.parse(json['onboardedAt'] as String),
      isActive: json['isActive'] as bool,
      monthlyRevenue: (json['monthlyRevenue'] as num).toDouble(),
      tier: json['tier'] as String,
    );

Map<String, dynamic> _$BusinessClientToJson(BusinessClient instance) =>
    <String, dynamic>{
      'id': instance.id,
      'companyName': instance.companyName,
      'industry': instance.industry,
      'contactEmail': instance.contactEmail,
      'contactPhone': instance.contactPhone,
      'onboardedAt': instance.onboardedAt.toIso8601String(),
      'isActive': instance.isActive,
      'monthlyRevenue': instance.monthlyRevenue,
      'tier': instance.tier,
    };

RevenueRecord _$RevenueRecordFromJson(Map<String, dynamic> json) =>
    RevenueRecord(
      id: json['id'] as String,
      clientId: json['clientId'] as String,
      userId: json['userId'] as String,
      amount: (json['amount'] as num).toDouble(),
      source: json['source'] as String,
      type: $enumDecode(_$RevenueTypeEnumMap, json['type']),
      date: DateTime.parse(json['date'] as String),
      currency: json['currency'] as String,
    );

Map<String, dynamic> _$RevenueRecordToJson(RevenueRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'clientId': instance.clientId,
      'userId': instance.userId,
      'amount': instance.amount,
      'source': instance.source,
      'type': _$RevenueTypeEnumMap[instance.type]!,
      'date': instance.date.toIso8601String(),
      'currency': instance.currency,
    };

const _$RevenueTypeEnumMap = {
  RevenueType.subscription: 'subscription',
  RevenueType.oneTime: 'oneTime',
  RevenueType.commission: 'commission',
  RevenueType.advertising: 'advertising',
};

UserEngagementMetric _$UserEngagementMetricFromJson(
        Map<String, dynamic> json) =>
    UserEngagementMetric(
      id: json['id'] as String,
      userId: json['userId'] as String,
      eventType: json['eventType'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      sessionDuration:
          Duration(microseconds: (json['sessionDuration'] as num).toInt()),
      pageViews: (json['pageViews'] as num).toInt(),
      properties: json['properties'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$UserEngagementMetricToJson(
        UserEngagementMetric instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'eventType': instance.eventType,
      'timestamp': instance.timestamp.toIso8601String(),
      'sessionDuration': instance.sessionDuration.inMicroseconds,
      'pageViews': instance.pageViews,
      'properties': instance.properties,
    };

BusinessReport _$BusinessReportFromJson(Map<String, dynamic> json) =>
    BusinessReport(
      id: json['id'] as String,
      name: json['name'] as String,
      type: $enumDecode(_$ReportTypeEnumMap, json['type']),
      fromDate: DateTime.parse(json['fromDate'] as String),
      toDate: DateTime.parse(json['toDate'] as String),
      data: json['data'] as Map<String, dynamic>,
      metrics:
          (json['metrics'] as List<dynamic>).map((e) => e as String).toList(),
      dimensions: (json['dimensions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      generatedAt: DateTime.parse(json['generatedAt'] as String),
      generatedBy: json['generatedBy'] as String,
    );

Map<String, dynamic> _$BusinessReportToJson(BusinessReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$ReportTypeEnumMap[instance.type]!,
      'fromDate': instance.fromDate.toIso8601String(),
      'toDate': instance.toDate.toIso8601String(),
      'data': instance.data,
      'metrics': instance.metrics,
      'dimensions': instance.dimensions,
      'generatedAt': instance.generatedAt.toIso8601String(),
      'generatedBy': instance.generatedBy,
    };

BusinessDashboard _$BusinessDashboardFromJson(Map<String, dynamic> json) =>
    BusinessDashboard(
      periodStart: DateTime.parse(json['periodStart'] as String),
      periodEnd: DateTime.parse(json['periodEnd'] as String),
      clientId: json['clientId'] as String?,
      revenueMetrics: RevenueMetrics.fromJson(
          json['revenueMetrics'] as Map<String, dynamic>),
      userMetrics:
          UserMetrics.fromJson(json['userMetrics'] as Map<String, dynamic>),
      engagementMetrics: EngagementMetrics.fromJson(
          json['engagementMetrics'] as Map<String, dynamic>),
      retentionMetrics: RetentionMetrics.fromJson(
          json['retentionMetrics'] as Map<String, dynamic>),
      conversionMetrics: ConversionMetrics.fromJson(
          json['conversionMetrics'] as Map<String, dynamic>),
      performanceMetrics: PerformanceMetrics.fromJson(
          json['performanceMetrics'] as Map<String, dynamic>),
      competitorAnalysis: CompetitorAnalysis.fromJson(
          json['competitorAnalysis'] as Map<String, dynamic>),
      forecasting: BusinessForecasting.fromJson(
          json['forecasting'] as Map<String, dynamic>),
      generatedAt: DateTime.parse(json['generatedAt'] as String),
    );

Map<String, dynamic> _$BusinessDashboardToJson(BusinessDashboard instance) =>
    <String, dynamic>{
      'periodStart': instance.periodStart.toIso8601String(),
      'periodEnd': instance.periodEnd.toIso8601String(),
      'clientId': instance.clientId,
      'revenueMetrics': instance.revenueMetrics,
      'userMetrics': instance.userMetrics,
      'engagementMetrics': instance.engagementMetrics,
      'retentionMetrics': instance.retentionMetrics,
      'conversionMetrics': instance.conversionMetrics,
      'performanceMetrics': instance.performanceMetrics,
      'competitorAnalysis': instance.competitorAnalysis,
      'forecasting': instance.forecasting,
      'generatedAt': instance.generatedAt.toIso8601String(),
    };

RevenueMetrics _$RevenueMetricsFromJson(Map<String, dynamic> json) =>
    RevenueMetrics(
      totalRevenue: (json['totalRevenue'] as num).toDouble(),
      subscriptionRevenue: (json['subscriptionRevenue'] as num).toDouble(),
      oneTimeRevenue: (json['oneTimeRevenue'] as num).toDouble(),
      growthRate: (json['growthRate'] as num).toDouble(),
      arr: (json['arr'] as num).toDouble(),
      arpu: (json['arpu'] as num).toDouble(),
      revenueBySource: (json['revenueBySource'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      monthlyTrend: (json['monthlyTrend'] as List<dynamic>)
          .map((e) => MonthlyRevenue.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$RevenueMetricsToJson(RevenueMetrics instance) =>
    <String, dynamic>{
      'totalRevenue': instance.totalRevenue,
      'subscriptionRevenue': instance.subscriptionRevenue,
      'oneTimeRevenue': instance.oneTimeRevenue,
      'growthRate': instance.growthRate,
      'arr': instance.arr,
      'arpu': instance.arpu,
      'revenueBySource': instance.revenueBySource,
      'monthlyTrend': instance.monthlyTrend,
    };

MonthlyRevenue _$MonthlyRevenueFromJson(Map<String, dynamic> json) =>
    MonthlyRevenue(
      year: (json['year'] as num).toInt(),
      month: (json['month'] as num).toInt(),
      revenue: (json['revenue'] as num).toDouble(),
    );

Map<String, dynamic> _$MonthlyRevenueToJson(MonthlyRevenue instance) =>
    <String, dynamic>{
      'year': instance.year,
      'month': instance.month,
      'revenue': instance.revenue,
    };

UserMetrics _$UserMetricsFromJson(Map<String, dynamic> json) => UserMetrics(
      totalUsers: (json['totalUsers'] as num).toInt(),
      activeUsers: (json['activeUsers'] as num).toInt(),
      newUsers: (json['newUsers'] as num).toInt(),
      premiumUsers: (json['premiumUsers'] as num).toInt(),
      userGrowthRate: (json['userGrowthRate'] as num).toDouble(),
      usersByCountry: Map<String, int>.from(json['usersByCountry'] as Map),
      usersByPlatform: Map<String, int>.from(json['usersByPlatform'] as Map),
      userAcquisitionChannels:
          (json['userAcquisitionChannels'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$UserMetricsToJson(UserMetrics instance) =>
    <String, dynamic>{
      'totalUsers': instance.totalUsers,
      'activeUsers': instance.activeUsers,
      'newUsers': instance.newUsers,
      'premiumUsers': instance.premiumUsers,
      'userGrowthRate': instance.userGrowthRate,
      'usersByCountry': instance.usersByCountry,
      'usersByPlatform': instance.usersByPlatform,
      'userAcquisitionChannels': instance.userAcquisitionChannels,
    };

EngagementMetrics _$EngagementMetricsFromJson(Map<String, dynamic> json) =>
    EngagementMetrics(
      dailyActiveUsers: (json['dailyActiveUsers'] as num).toInt(),
      weeklyActiveUsers: (json['weeklyActiveUsers'] as num).toInt(),
      monthlyActiveUsers: (json['monthlyActiveUsers'] as num).toInt(),
      averageSessionDuration: Duration(
          microseconds: (json['averageSessionDuration'] as num).toInt()),
      sessionsPerUser: (json['sessionsPerUser'] as num).toDouble(),
      bounceRate: (json['bounceRate'] as num).toDouble(),
      pageViews: (json['pageViews'] as num).toInt(),
      featureUsage: (json['featureUsage'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      userJourney: (json['userJourney'] as List<dynamic>)
          .map((e) => UserJourneyStep.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$EngagementMetricsToJson(EngagementMetrics instance) =>
    <String, dynamic>{
      'dailyActiveUsers': instance.dailyActiveUsers,
      'weeklyActiveUsers': instance.weeklyActiveUsers,
      'monthlyActiveUsers': instance.monthlyActiveUsers,
      'averageSessionDuration': instance.averageSessionDuration.inMicroseconds,
      'sessionsPerUser': instance.sessionsPerUser,
      'bounceRate': instance.bounceRate,
      'pageViews': instance.pageViews,
      'featureUsage': instance.featureUsage,
      'userJourney': instance.userJourney,
    };

UserJourneyStep _$UserJourneyStepFromJson(Map<String, dynamic> json) =>
    UserJourneyStep(
      json['name'] as String,
      (json['conversionRate'] as num).toDouble(),
      Duration(microseconds: (json['averageTime'] as num).toInt()),
    );

Map<String, dynamic> _$UserJourneyStepToJson(UserJourneyStep instance) =>
    <String, dynamic>{
      'name': instance.name,
      'conversionRate': instance.conversionRate,
      'averageTime': instance.averageTime.inMicroseconds,
    };

RetentionMetrics _$RetentionMetricsFromJson(Map<String, dynamic> json) =>
    RetentionMetrics(
      day1Retention: (json['day1Retention'] as num).toDouble(),
      day7Retention: (json['day7Retention'] as num).toDouble(),
      day30Retention: (json['day30Retention'] as num).toDouble(),
      cohortAnalysis: (json['cohortAnalysis'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            k, (e as List<dynamic>).map((e) => (e as num).toDouble()).toList()),
      ),
      churnRate: (json['churnRate'] as num).toDouble(),
      ltv: (json['ltv'] as num).toDouble(),
      retentionBySegment:
          (json['retentionBySegment'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$RetentionMetricsToJson(RetentionMetrics instance) =>
    <String, dynamic>{
      'day1Retention': instance.day1Retention,
      'day7Retention': instance.day7Retention,
      'day30Retention': instance.day30Retention,
      'cohortAnalysis': instance.cohortAnalysis,
      'churnRate': instance.churnRate,
      'ltv': instance.ltv,
      'retentionBySegment': instance.retentionBySegment,
    };

ConversionMetrics _$ConversionMetricsFromJson(Map<String, dynamic> json) =>
    ConversionMetrics(
      trialToSubscription: (json['trialToSubscription'] as num).toDouble(),
      freeToTrial: (json['freeToTrial'] as num).toDouble(),
      visitorToSignup: (json['visitorToSignup'] as num).toDouble(),
      conversionFunnel: (json['conversionFunnel'] as List<dynamic>)
          .map((e) => ConversionStep.fromJson(e as Map<String, dynamic>))
          .toList(),
      conversionByChannel:
          (json['conversionByChannel'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      timeToConversion:
          Duration(microseconds: (json['timeToConversion'] as num).toInt()),
    );

Map<String, dynamic> _$ConversionMetricsToJson(ConversionMetrics instance) =>
    <String, dynamic>{
      'trialToSubscription': instance.trialToSubscription,
      'freeToTrial': instance.freeToTrial,
      'visitorToSignup': instance.visitorToSignup,
      'conversionFunnel': instance.conversionFunnel,
      'conversionByChannel': instance.conversionByChannel,
      'timeToConversion': instance.timeToConversion.inMicroseconds,
    };

ConversionStep _$ConversionStepFromJson(Map<String, dynamic> json) =>
    ConversionStep(
      json['name'] as String,
      (json['users'] as num).toInt(),
      (json['conversionRate'] as num).toDouble(),
    );

Map<String, dynamic> _$ConversionStepToJson(ConversionStep instance) =>
    <String, dynamic>{
      'name': instance.name,
      'users': instance.users,
      'conversionRate': instance.conversionRate,
    };

PerformanceMetrics _$PerformanceMetricsFromJson(Map<String, dynamic> json) =>
    PerformanceMetrics(
      averageLoadTime: (json['averageLoadTime'] as num).toDouble(),
      uptime: (json['uptime'] as num).toDouble(),
      errorRate: (json['errorRate'] as num).toInt(),
      apiResponseTimes: (json['apiResponseTimes'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      errorsByType: Map<String, int>.from(json['errorsByType'] as Map),
      crashRate: (json['crashRate'] as num).toDouble(),
    );

Map<String, dynamic> _$PerformanceMetricsToJson(PerformanceMetrics instance) =>
    <String, dynamic>{
      'averageLoadTime': instance.averageLoadTime,
      'uptime': instance.uptime,
      'errorRate': instance.errorRate,
      'apiResponseTimes': instance.apiResponseTimes,
      'errorsByType': instance.errorsByType,
      'crashRate': instance.crashRate,
    };

CompetitorAnalysis _$CompetitorAnalysisFromJson(Map<String, dynamic> json) =>
    CompetitorAnalysis(
      competitors: (json['competitors'] as List<dynamic>)
          .map((e) => CompetitorData.fromJson(e as Map<String, dynamic>))
          .toList(),
      marketShare: (json['marketShare'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      featureComparison:
          (json['featureComparison'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      pricingComparison:
          (json['pricingComparison'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$CompetitorAnalysisToJson(CompetitorAnalysis instance) =>
    <String, dynamic>{
      'competitors': instance.competitors,
      'marketShare': instance.marketShare,
      'featureComparison': instance.featureComparison,
      'pricingComparison': instance.pricingComparison,
    };

CompetitorData _$CompetitorDataFromJson(Map<String, dynamic> json) =>
    CompetitorData(
      name: json['name'] as String,
      marketShare: (json['marketShare'] as num).toDouble(),
      rating: (json['rating'] as num).toDouble(),
      strengths:
          (json['strengths'] as List<dynamic>).map((e) => e as String).toList(),
      weaknesses: (json['weaknesses'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$CompetitorDataToJson(CompetitorData instance) =>
    <String, dynamic>{
      'name': instance.name,
      'marketShare': instance.marketShare,
      'rating': instance.rating,
      'strengths': instance.strengths,
      'weaknesses': instance.weaknesses,
    };

BusinessForecasting _$BusinessForecastingFromJson(Map<String, dynamic> json) =>
    BusinessForecasting(
      revenueForecast: (json['revenueForecast'] as List<dynamic>)
          .map((e) => RevenueForecast.fromJson(e as Map<String, dynamic>))
          .toList(),
      userGrowthForecast: (json['userGrowthForecast'] as List<dynamic>)
          .map((e) => UserGrowthForecast.fromJson(e as Map<String, dynamic>))
          .toList(),
      seasonalTrends: (json['seasonalTrends'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      confidenceLevel: (json['confidenceLevel'] as num).toDouble(),
    );

Map<String, dynamic> _$BusinessForecastingToJson(
        BusinessForecasting instance) =>
    <String, dynamic>{
      'revenueForecast': instance.revenueForecast,
      'userGrowthForecast': instance.userGrowthForecast,
      'seasonalTrends': instance.seasonalTrends,
      'confidenceLevel': instance.confidenceLevel,
    };

RevenueForecast _$RevenueForecastFromJson(Map<String, dynamic> json) =>
    RevenueForecast(
      DateTime.parse(json['date'] as String),
      (json['predictedRevenue'] as num).toDouble(),
      (json['confidence'] as num).toDouble(),
    );

Map<String, dynamic> _$RevenueForecastToJson(RevenueForecast instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'predictedRevenue': instance.predictedRevenue,
      'confidence': instance.confidence,
    };

UserGrowthForecast _$UserGrowthForecastFromJson(Map<String, dynamic> json) =>
    UserGrowthForecast(
      DateTime.parse(json['date'] as String),
      (json['predictedUsers'] as num).toInt(),
      (json['confidence'] as num).toDouble(),
    );

Map<String, dynamic> _$UserGrowthForecastToJson(UserGrowthForecast instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'predictedUsers': instance.predictedUsers,
      'confidence': instance.confidence,
    };

MarketTrend _$MarketTrendFromJson(Map<String, dynamic> json) => MarketTrend(
      json['name'] as String,
      (json['growthRate'] as num).toDouble(),
      $enumDecode(_$TrendDirectionEnumMap, json['direction']),
    );

Map<String, dynamic> _$MarketTrendToJson(MarketTrend instance) =>
    <String, dynamic>{
      'name': instance.name,
      'growthRate': instance.growthRate,
      'direction': _$TrendDirectionEnumMap[instance.direction]!,
    };

const _$TrendDirectionEnumMap = {
  TrendDirection.up: 'up',
  TrendDirection.down: 'down',
  TrendDirection.stable: 'stable',
};

ThreatLevel _$ThreatLevelFromJson(Map<String, dynamic> json) => ThreatLevel(
      json['name'] as String,
      $enumDecode(_$ThreatSeverityEnumMap, json['severity']),
      (json['probability'] as num).toDouble(),
    );

Map<String, dynamic> _$ThreatLevelToJson(ThreatLevel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'severity': _$ThreatSeverityEnumMap[instance.severity]!,
      'probability': instance.probability,
    };

const _$ThreatSeverityEnumMap = {
  ThreatSeverity.low: 'low',
  ThreatSeverity.medium: 'medium',
  ThreatSeverity.high: 'high',
  ThreatSeverity.critical: 'critical',
};

RevenueProjection _$RevenueProjectionFromJson(Map<String, dynamic> json) =>
    RevenueProjection(
      month: (json['month'] as num).toInt(),
      projectedRevenue: (json['projectedRevenue'] as num).toDouble(),
      confidence: (json['confidence'] as num).toDouble(),
    );

Map<String, dynamic> _$RevenueProjectionToJson(RevenueProjection instance) =>
    <String, dynamic>{
      'month': instance.month,
      'projectedRevenue': instance.projectedRevenue,
      'confidence': instance.confidence,
    };

UserGrowthProjection _$UserGrowthProjectionFromJson(
        Map<String, dynamic> json) =>
    UserGrowthProjection(
      month: (json['month'] as num).toInt(),
      projectedUsers: (json['projectedUsers'] as num).toInt(),
      organicGrowth: (json['organicGrowth'] as num).toDouble(),
      paidGrowth: (json['paidGrowth'] as num).toDouble(),
    );

Map<String, dynamic> _$UserGrowthProjectionToJson(
        UserGrowthProjection instance) =>
    <String, dynamic>{
      'month': instance.month,
      'projectedUsers': instance.projectedUsers,
      'organicGrowth': instance.organicGrowth,
      'paidGrowth': instance.paidGrowth,
    };

MarketSizeProjection _$MarketSizeProjectionFromJson(
        Map<String, dynamic> json) =>
    MarketSizeProjection(
      currentMarketSize: (json['currentMarketSize'] as num).toDouble(),
      projectedMarketSize: (json['projectedMarketSize'] as num).toDouble(),
      cagr: (json['cagr'] as num).toDouble(),
      addressableMarket: (json['addressableMarket'] as num).toDouble(),
    );

Map<String, dynamic> _$MarketSizeProjectionToJson(
        MarketSizeProjection instance) =>
    <String, dynamic>{
      'currentMarketSize': instance.currentMarketSize,
      'projectedMarketSize': instance.projectedMarketSize,
      'cagr': instance.cagr,
      'addressableMarket': instance.addressableMarket,
    };
