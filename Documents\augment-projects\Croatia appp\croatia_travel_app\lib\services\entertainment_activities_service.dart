import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/entertainment_activities.dart';

/// Služba pro Entertainment & Activities - kompletní zábavní služby
/// Právně bezpečné řešení bez API závislostí
class EntertainmentActivitiesService {
  static final EntertainmentActivitiesService _instance =
      EntertainmentActivitiesService._internal();
  factory EntertainmentActivitiesService() => _instance;
  EntertainmentActivitiesService._internal();

  // Cache pro zábavní aktivity
  List<EntertainmentActivity>? _cachedActivities;
  List<AmusementPark>? _cachedAmusementParks;
  List<OutdoorActivity>? _cachedOutdoorActivities;
  List<NightlifeVenue>? _cachedNightlifeVenues;
  List<FamilyActivity>? _cachedFamilyActivities;
  DateTime? _lastCacheUpdate;
  final Duration _cacheValidDuration = const Duration(hours: 2);

  /// Získá všechny zábavní aktivity
  Future<List<EntertainmentActivity>> getAllActivities() async {
    if (_isCacheValid() && _cachedActivities != null) {
      return _cachedActivities!;
    }

    try {
      await Future.delayed(const Duration(milliseconds: 800));

      _cachedActivities = _generateSampleActivities();
      _lastCacheUpdate = DateTime.now();

      return _cachedActivities!;
    } catch (e) {
      debugPrint('Chyba při načítání aktivit: $e');
      return [];
    }
  }

  /// Získá všechny zábavní parky
  Future<List<AmusementPark>> getAllAmusementParks() async {
    if (_isCacheValid() && _cachedAmusementParks != null) {
      return _cachedAmusementParks!;
    }

    try {
      await Future.delayed(const Duration(milliseconds: 600));

      _cachedAmusementParks = _generateSampleAmusementParks();
      _lastCacheUpdate = DateTime.now();

      return _cachedAmusementParks!;
    } catch (e) {
      debugPrint('Chyba při načítání zábavních parků: $e');
      return [];
    }
  }

  /// Získá všechny outdoor aktivity
  Future<List<OutdoorActivity>> getAllOutdoorActivities() async {
    if (_isCacheValid() && _cachedOutdoorActivities != null) {
      return _cachedOutdoorActivities!;
    }

    try {
      await Future.delayed(const Duration(milliseconds: 700));

      _cachedOutdoorActivities = _generateSampleOutdoorActivities();
      _lastCacheUpdate = DateTime.now();

      return _cachedOutdoorActivities!;
    } catch (e) {
      debugPrint('Chyba při načítání outdoor aktivit: $e');
      return [];
    }
  }

  /// Získá všechny noční podniky
  Future<List<NightlifeVenue>> getAllNightlifeVenues() async {
    if (_isCacheValid() && _cachedNightlifeVenues != null) {
      return _cachedNightlifeVenues!;
    }

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      _cachedNightlifeVenues = _generateSampleNightlifeVenues();
      _lastCacheUpdate = DateTime.now();

      return _cachedNightlifeVenues!;
    } catch (e) {
      debugPrint('Chyba při načítání nočního života: $e');
      return [];
    }
  }

  /// Získá všechny rodinné aktivity
  Future<List<FamilyActivity>> getAllFamilyActivities() async {
    if (_isCacheValid() && _cachedFamilyActivities != null) {
      return _cachedFamilyActivities!;
    }

    try {
      await Future.delayed(const Duration(milliseconds: 400));

      _cachedFamilyActivities = _generateSampleFamilyActivities();
      _lastCacheUpdate = DateTime.now();

      return _cachedFamilyActivities!;
    } catch (e) {
      debugPrint('Chyba při načítání rodinných aktivit: $e');
      return [];
    }
  }

  /// Vyhledá aktivity podle kritérií
  Future<List<EntertainmentActivity>> searchActivities({
    String? query,
    String? region,
    EntertainmentActivityType? activityType,
    AgeGroup? targetAgeGroup,
    ActivityDifficulty? difficulty,
    bool? isFamilyFriendly,
    bool? isIndoor,
    double? latitude,
    double? longitude,
    double? maxDistance,
    double? minRating,
  }) async {
    final allActivities = await getAllActivities();

    return allActivities.where((activity) {
      if (query != null && query.isNotEmpty) {
        final searchLower = query.toLowerCase();
        if (!activity.name.toLowerCase().contains(searchLower) &&
            !activity.description.toLowerCase().contains(searchLower)) {
          return false;
        }
      }

      if (region != null && region != 'all' && activity.region != region) {
        return false;
      }

      if (activityType != null && activity.activityType != activityType) {
        return false;
      }

      if (targetAgeGroup != null &&
          activity.targetAgeGroup != targetAgeGroup &&
          activity.targetAgeGroup != AgeGroup.allAges) {
        return false;
      }

      if (difficulty != null && activity.difficulty != difficulty) {
        return false;
      }

      if (isFamilyFriendly == true && !activity.isFamilyFriendly) return false;
      if (isIndoor != null && activity.isIndoor != isIndoor) return false;

      if (latitude != null && longitude != null && maxDistance != null) {
        final distance = activity.distanceFrom(latitude, longitude);
        if (distance > maxDistance) return false;
      }

      if (minRating != null && activity.rating < minRating) return false;

      return true;
    }).toList();
  }

  /// Získá nejbližší aktivity
  Future<List<EntertainmentActivity>> getNearbyActivities(
    double latitude,
    double longitude, {
    double maxDistance = 15.0,
    EntertainmentActivityType? activityType,
    int limit = 20,
  }) async {
    final activities = await getAllActivities();

    final nearbyActivities = activities.where((activity) {
      if (activityType != null && activity.activityType != activityType) {
        return false;
      }

      final distance = activity.distanceFrom(latitude, longitude);
      return distance <= maxDistance;
    }).toList();

    // Seřadí podle vzdálenosti
    nearbyActivities.sort((a, b) {
      final distanceA = a.distanceFrom(latitude, longitude);
      final distanceB = b.distanceFrom(latitude, longitude);
      return distanceA.compareTo(distanceB);
    });

    return nearbyActivities.take(limit).toList();
  }

  /// Získá statistiky zábavních aktivit
  Future<EntertainmentStatistics> getEntertainmentStatistics() async {
    final activities = await getAllActivities();
    final amusementParks = await getAllAmusementParks();
    final outdoorActivities = await getAllOutdoorActivities();
    final nightlifeVenues = await getAllNightlifeVenues();
    final familyActivities = await getAllFamilyActivities();

    return EntertainmentStatistics(
      totalActivities: activities.length,
      totalAmusementParks: amusementParks.length,
      totalOutdoorActivities: outdoorActivities.length,
      totalNightlifeVenues: nightlifeVenues.length,
      totalFamilyActivities: familyActivities.length,
      topRatedActivities: activities.where((a) => a.rating >= 4.0).length,
      familyFriendlyActivities: activities
          .where((a) => a.isFamilyFriendly)
          .length,
      indoorActivities: activities.where((a) => a.isIndoor).length,
      averageRating: activities.isNotEmpty
          ? activities.map((a) => a.rating).reduce((a, b) => a + b) /
                activities.length
          : 0.0,
    );
  }

  /// Kontroluje platnost cache
  bool _isCacheValid() {
    return _lastCacheUpdate != null &&
        DateTime.now().difference(_lastCacheUpdate!) < _cacheValidDuration;
  }

  /// Generuje ukázková data aktivit
  List<EntertainmentActivity> _generateSampleActivities() {
    final activities = <EntertainmentActivity>[];

    // Zagreb aktivity
    activities.addAll([
      _createActivity(
        'Aquapark Adamovec',
        'Největší aquapark v okolí Záhřebu s tobogány a bazény',
        EntertainmentActivityType.waterPark,
        'Adamovec 1, Adamovec',
        45.9167,
        16.0833,
        'zagreb',
        4.3,
        245,
        ['Tobogány', 'Dětský bazén', 'Wellness', 'Restaurace'],
        isFamilyFriendly: true,
        isIndoor: false,
      ),
      _createActivity(
        'Maksimir Park',
        'Největší park v Záhřebu s zoo a jezery',
        EntertainmentActivityType.hiking,
        'Maksimirski perivoj, Zagreb',
        45.8264,
        16.0175,
        'zagreb',
        4.5,
        567,
        ['Pěší stezky', 'Zoo', 'Jezera', 'Piknik'],
        isFamilyFriendly: true,
        isIndoor: false,
      ),
      _createActivity(
        'Escape Room Zagreb',
        'Napínavé únikové hry v centru města',
        EntertainmentActivityType.amusementPark,
        'Ilica 25, Zagreb',
        45.8131,
        15.9775,
        'zagreb',
        4.2,
        189,
        ['Únikové hry', 'Týmová hra', 'Logické hádanky'],
        isFamilyFriendly: true,
        isIndoor: true,
      ),
    ]);

    // Split aktivity
    activities.addAll([
      _createActivity(
        'Marjan Hill',
        'Zelená oáza Splitu s výhledy na město a moře',
        EntertainmentActivityType.hiking,
        'Marjan, Split',
        43.5147,
        16.4435,
        'dalmatia',
        4.6,
        423,
        ['Hiking', 'Výhledy', 'Pláže', 'Kláštery'],
        isFamilyFriendly: true,
        isIndoor: false,
      ),
      _createActivity(
        'Aquapark Solaris',
        'Moderní aquapark s mořskou vodou',
        EntertainmentActivityType.waterPark,
        'Šibenik',
        43.7350,
        15.8952,
        'dalmatia',
        4.4,
        312,
        ['Mořská voda', 'Tobogány', 'Lazy river', 'Wellness'],
        isFamilyFriendly: true,
        isIndoor: false,
      ),
    ]);

    // Dubrovník aktivity
    activities.addAll([
      _createActivity(
        'Kayaking Elafiti',
        'Kajak výlet k ostrovům Elafiti',
        EntertainmentActivityType.kayaking,
        'Gruž Harbor, Dubrovnik',
        42.6629,
        18.0922,
        'dubrovnik',
        4.7,
        156,
        ['Kajak', 'Ostrovy', 'Snorkeling', 'Průvodce'],
        isFamilyFriendly: true,
        isIndoor: false,
      ),
    ]);

    // Istrie aktivity
    activities.addAll([
      _createActivity(
        'Istralandia',
        'Největší aquapark v Istrii',
        EntertainmentActivityType.waterPark,
        'Aquapark Istralandia, Novigrad',
        45.3167,
        13.5667,
        'istria',
        4.5,
        389,
        ['Tobogány', 'Dětská zóna', 'Adrenalin', 'Relax'],
        isFamilyFriendly: true,
        isIndoor: false,
      ),
      _createActivity(
        'Baredine Cave',
        'Podzemní jeskyně s krápníky',
        EntertainmentActivityType.amusementPark,
        'Nova Vas, Poreč',
        45.2333,
        13.6000,
        'istria',
        4.3,
        234,
        ['Jeskyně', 'Krápníky', 'Průvodce', 'Podzemní jezero'],
        isFamilyFriendly: true,
        isIndoor: true,
      ),
    ]);

    return activities;
  }

  EntertainmentActivity _createActivity(
    String name,
    String description,
    EntertainmentActivityType activityType,
    String address,
    double latitude,
    double longitude,
    String region,
    double rating,
    int reviewCount,
    List<String> features, {
    bool isFamilyFriendly = false,
    bool isIndoor = false,
  }) {
    return EntertainmentActivity(
      id: 'activity_${name.toLowerCase().replaceAll(' ', '_')}',
      name: name,
      description: description,
      activityType: activityType,
      address: address,
      latitude: latitude,
      longitude: longitude,
      region: region,
      rating: rating,
      reviewCount: reviewCount,
      features: features,
      operatingHours: '9:00 - 18:00',
      priceRange: '50-200 HRK',
      isActive: true,
      isFamilyFriendly: isFamilyFriendly,
      isIndoor: isIndoor,
      requiresBooking: false,
      targetAgeGroup: isFamilyFriendly ? AgeGroup.allAges : AgeGroup.adults,
      difficulty: ActivityDifficulty.easy,
      estimatedDuration: const Duration(hours: 2),
      equipment: [],
      supportedLanguages: ['hr', 'en', 'de'],
      lastUpdated: DateTime.now(),
    );
  }

  /// Generuje ukázková data zábavních parků
  List<AmusementPark> _generateSampleAmusementParks() {
    return [
      AmusementPark(
        id: 'park_istralandia',
        name: 'Istralandia',
        description: 'Největší aquapark v Istrii s mnoha atrakcemi',
        address: 'Aquapark Istralandia, Novigrad',
        latitude: 45.3167,
        longitude: 13.5667,
        region: 'istria',
        rating: 4.5,
        reviewCount: 389,
        attractions: [
          Attraction(
            id: 'tobogan_kamikaze',
            name: 'Kamikaze',
            description: 'Extrémní tobogán pro odvážné',
            attractionType: AttractionType.waterSlide,
            minAge: 14,
            minHeight: 140,
            duration: const Duration(seconds: 30),
            capacity: 1,
            isOperational: true,
            safetyRequirements: [
              'Plavecké schopnosti',
              'Minimální výška 140cm',
            ],
          ),
          Attraction(
            id: 'detsky_bazen',
            name: 'Dětský bazén',
            description: 'Bezpečný bazén pro nejmenší',
            attractionType: AttractionType.pool,
            minAge: 0,
            minHeight: 0,
            duration: const Duration(hours: 2),
            capacity: 50,
            isOperational: true,
            safetyRequirements: ['Dohled rodičů'],
          ),
        ],
        operatingHours: '9:00 - 19:00',
        priceRange: '150-250 HRK',
        hasRestaurants: true,
        hasParking: true,
        hasDisabledAccess: true,
        hasFirstAid: true,
        minAge: 0,
        maxCapacity: 2000,
        safetyFeatures: ['Plavčíci', 'První pomoc', 'Bezpečnostní kamery'],
        phone: '+385 52 757 131',
        website: 'https://www.istralandia.hr',
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  /// Generuje ukázková data outdoor aktivit
  List<OutdoorActivity> _generateSampleOutdoorActivities() {
    return [
      OutdoorActivity(
        id: 'hiking_marjan',
        name: 'Marjan Hill Trail',
        description: 'Krásná pěší trasa s výhledy na Split',
        activityType: OutdoorActivityType.hiking,
        location: 'Marjan, Split',
        latitude: 43.5147,
        longitude: 16.4435,
        region: 'dalmatia',
        rating: 4.6,
        reviewCount: 423,
        difficulty: ActivityDifficulty.easy,
        estimatedDuration: const Duration(hours: 2),
        distance: 5.2,
        elevation: 178,
        equipment: ['Pohodlná obuv', 'Voda', 'Kšiltovka'],
        safetyTips: ['Vezměte si dostatek vody', 'Chraňte se před sluncem'],
        bestSeason: 'Jaro, podzim',
        weatherRequirements: 'Suché počasí',
        requiresGuide: false,
        lastUpdated: DateTime.now(),
      ),
      OutdoorActivity(
        id: 'kayaking_elafiti',
        name: 'Elafiti Islands Kayaking',
        description: 'Kajak výlet k ostrovům Elafiti',
        activityType: OutdoorActivityType.kayaking,
        location: 'Gruž Harbor, Dubrovnik',
        latitude: 42.6629,
        longitude: 18.0922,
        region: 'dubrovnik',
        rating: 4.7,
        reviewCount: 156,
        difficulty: ActivityDifficulty.beginner,
        estimatedDuration: const Duration(hours: 4),
        distance: 12.0,
        elevation: 0,
        equipment: ['Kajak', 'Pádlo', 'Záchranná vesta', 'Snorkel'],
        safetyTips: ['Umět plavat', 'Sledovat počasí', 'Zůstat ve skupině'],
        bestSeason: 'Květen - září',
        weatherRequirements: 'Klidné moře',
        requiresGuide: true,
        guideContact: '+385 20 123 456',
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  /// Generuje ukázková data nočního života
  List<NightlifeVenue> _generateSampleNightlifeVenues() {
    return [
      NightlifeVenue(
        id: 'club_aquarius',
        name: 'Aquarius Club',
        description: 'Nejznámější noční klub v Záhřebu',
        venueType: NightlifeType.nightclub,
        address: 'Aleja Matije Ljubeka, Zagreb',
        latitude: 45.7833,
        longitude: 15.9667,
        region: 'zagreb',
        rating: 4.2,
        reviewCount: 567,
        operatingHours: '22:00 - 06:00',
        minAge: 18,
        hasLiveMusic: true,
        hasDanceFloor: true,
        hasOutdoorSeating: true,
        acceptsReservations: true,
        musicGenres: ['Electronic', 'House', 'Techno'],
        drinkSpecialties: ['Koktejly', 'Premium alkohol'],
        dressCode: 'Smart casual',
        phone: '+385 1 364 0231',
        website: 'https://www.aquarius.hr',
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  /// Generuje ukázková data rodinných aktivit
  List<FamilyActivity> _generateSampleFamilyActivities() {
    return [
      FamilyActivity(
        id: 'zoo_zagreb',
        name: 'Zagreb Zoo',
        description: 'Zoologická zahrada v Maksimiru',
        activityType: FamilyActivityType.zoo,
        address: 'Maksimirski perivoj bb, Zagreb',
        latitude: 45.8264,
        longitude: 16.0175,
        region: 'zagreb',
        rating: 4.3,
        reviewCount: 789,
        targetAgeGroup: AgeGroup.allAges,
        operatingHours: '9:00 - 17:00',
        priceRange: '30-50 HRK',
        hasParking: true,
        hasRestrooms: true,
        hasDisabledAccess: true,
        hasStrollerAccess: true,
        safetyFeatures: ['Oplotěné výběhy', 'Bezpečnostní personál'],
        phone: '+385 1 230 2198',
        website: 'https://www.zoo.hr',
        lastUpdated: DateTime.now(),
      ),
    ];
  }
}

/// Statistiky zábavních aktivit
class EntertainmentStatistics {
  final int totalActivities;
  final int totalAmusementParks;
  final int totalOutdoorActivities;
  final int totalNightlifeVenues;
  final int totalFamilyActivities;
  final int topRatedActivities;
  final int familyFriendlyActivities;
  final int indoorActivities;
  final double averageRating;

  EntertainmentStatistics({
    required this.totalActivities,
    required this.totalAmusementParks,
    required this.totalOutdoorActivities,
    required this.totalNightlifeVenues,
    required this.totalFamilyActivities,
    required this.topRatedActivities,
    required this.familyFriendlyActivities,
    required this.indoorActivities,
    required this.averageRating,
  });
}
