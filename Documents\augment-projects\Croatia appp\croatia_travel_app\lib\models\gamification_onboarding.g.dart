// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'gamification_onboarding.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OnboardingQuest _$OnboardingQuestFromJson(Map<String, dynamic> json) =>
    OnboardingQuest(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      difficulty: (json['difficulty'] as num).toInt(),
      estimatedMinutes: (json['estimatedMinutes'] as num).toInt(),
      steps: (json['steps'] as List<dynamic>).map((e) => e as String).toList(),
      requirements: json['requirements'] as Map<String, dynamic>,
      rewards: json['rewards'] as Map<String, dynamic>,
      isOptional: json['isOptional'] as bool,
      prerequisites: (json['prerequisites'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$OnboardingQuestToJson(OnboardingQuest instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'category': instance.category,
      'difficulty': instance.difficulty,
      'estimatedMinutes': instance.estimatedMinutes,
      'steps': instance.steps,
      'requirements': instance.requirements,
      'rewards': instance.rewards,
      'isOptional': instance.isOptional,
      'prerequisites': instance.prerequisites,
    };

OnboardingMilestone _$OnboardingMilestoneFromJson(Map<String, dynamic> json) =>
    OnboardingMilestone(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      requiredQuests: (json['requiredQuests'] as num).toInt(),
      questIds:
          (json['questIds'] as List<dynamic>).map((e) => e as String).toList(),
      rewards: json['rewards'] as Map<String, dynamic>,
      badgeIcon: json['badgeIcon'] as String?,
      isCompleted: json['isCompleted'] as bool,
    );

Map<String, dynamic> _$OnboardingMilestoneToJson(
        OnboardingMilestone instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'requiredQuests': instance.requiredQuests,
      'questIds': instance.questIds,
      'rewards': instance.rewards,
      'badgeIcon': instance.badgeIcon,
      'isCompleted': instance.isCompleted,
    };

UserOnboardingProgress _$UserOnboardingProgressFromJson(
        Map<String, dynamic> json) =>
    UserOnboardingProgress(
      userId: json['userId'] as String,
      journeyId: json['journeyId'] as String,
      completedQuests: (json['completedQuests'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      completedMilestones: (json['completedMilestones'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      totalPoints: (json['totalPoints'] as num).toInt(),
      currentStreak: (json['currentStreak'] as num).toInt(),
      startedAt: DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      personality:
          $enumDecode(_$OnboardingPersonalityEnumMap, json['personality']),
      preferences: json['preferences'] as Map<String, dynamic>,
      categoryProgress: Map<String, int>.from(json['categoryProgress'] as Map),
    );

Map<String, dynamic> _$UserOnboardingProgressToJson(
        UserOnboardingProgress instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'journeyId': instance.journeyId,
      'completedQuests': instance.completedQuests,
      'completedMilestones': instance.completedMilestones,
      'totalPoints': instance.totalPoints,
      'currentStreak': instance.currentStreak,
      'startedAt': instance.startedAt.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'personality': _$OnboardingPersonalityEnumMap[instance.personality]!,
      'preferences': instance.preferences,
      'categoryProgress': instance.categoryProgress,
    };

const _$OnboardingPersonalityEnumMap = {
  OnboardingPersonality.explorer: 'explorer',
  OnboardingPersonality.achiever: 'achiever',
  OnboardingPersonality.socializer: 'socializer',
  OnboardingPersonality.competitor: 'competitor',
};

OnboardingJourney _$OnboardingJourneyFromJson(Map<String, dynamic> json) =>
    OnboardingJourney(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      quests: (json['quests'] as List<dynamic>)
          .map((e) => OnboardingQuest.fromJson(e as Map<String, dynamic>))
          .toList(),
      milestones: (json['milestones'] as List<dynamic>)
          .map((e) => OnboardingMilestone.fromJson(e as Map<String, dynamic>))
          .toList(),
      estimatedDays: (json['estimatedDays'] as num).toInt(),
      targetPersonality: $enumDecode(
          _$OnboardingPersonalityEnumMap, json['targetPersonality']),
      configuration: json['configuration'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$OnboardingJourneyToJson(OnboardingJourney instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'quests': instance.quests,
      'milestones': instance.milestones,
      'estimatedDays': instance.estimatedDays,
      'targetPersonality':
          _$OnboardingPersonalityEnumMap[instance.targetPersonality]!,
      'configuration': instance.configuration,
    };

QuestCompletionResult _$QuestCompletionResultFromJson(
        Map<String, dynamic> json) =>
    QuestCompletionResult(
      questId: json['questId'] as String,
      userId: json['userId'] as String,
      completedAt: DateTime.parse(json['completedAt'] as String),
      pointsEarned: (json['pointsEarned'] as num).toInt(),
      unlockedFeatures: (json['unlockedFeatures'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      earnedBadges: (json['earnedBadges'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      triggeredMilestone: json['triggeredMilestone'] as bool,
      nextRecommendedQuest: json['nextRecommendedQuest'] as String?,
    );

Map<String, dynamic> _$QuestCompletionResultToJson(
        QuestCompletionResult instance) =>
    <String, dynamic>{
      'questId': instance.questId,
      'userId': instance.userId,
      'completedAt': instance.completedAt.toIso8601String(),
      'pointsEarned': instance.pointsEarned,
      'unlockedFeatures': instance.unlockedFeatures,
      'earnedBadges': instance.earnedBadges,
      'triggeredMilestone': instance.triggeredMilestone,
      'nextRecommendedQuest': instance.nextRecommendedQuest,
    };

OnboardingEvent _$OnboardingEventFromJson(Map<String, dynamic> json) =>
    OnboardingEvent(
      id: json['id'] as String,
      type: $enumDecode(_$OnboardingEventTypeEnumMap, json['type']),
      userId: json['userId'] as String,
      questId: json['questId'] as String?,
      milestoneId: json['milestoneId'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      data: json['data'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$OnboardingEventToJson(OnboardingEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$OnboardingEventTypeEnumMap[instance.type]!,
      'userId': instance.userId,
      'questId': instance.questId,
      'milestoneId': instance.milestoneId,
      'timestamp': instance.timestamp.toIso8601String(),
      'data': instance.data,
    };

const _$OnboardingEventTypeEnumMap = {
  OnboardingEventType.questStarted: 'questStarted',
  OnboardingEventType.questCompleted: 'questCompleted',
  OnboardingEventType.milestoneReached: 'milestoneReached',
  OnboardingEventType.journeyCompleted: 'journeyCompleted',
  OnboardingEventType.achievementUnlocked: 'achievementUnlocked',
};

OnboardingCompletionReward _$OnboardingCompletionRewardFromJson(
        Map<String, dynamic> json) =>
    OnboardingCompletionReward(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      value: json['value'] as Map<String, dynamic>,
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      isRedeemed: json['isRedeemed'] as bool,
    );

Map<String, dynamic> _$OnboardingCompletionRewardToJson(
        OnboardingCompletionReward instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': instance.type,
      'value': instance.value,
      'expiresAt': instance.expiresAt?.toIso8601String(),
      'isRedeemed': instance.isRedeemed,
    };
