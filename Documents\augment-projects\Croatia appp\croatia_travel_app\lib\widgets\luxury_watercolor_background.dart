import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../theme/watercolor_theme.dart';

/// Luxusní watercolor pozadí inspirované reference obrázky
class LuxuryWatercolorBackground extends StatefulWidget {
  final Widget child;
  final List<Color>? colors;
  final bool animated;
  final Duration animationDuration;
  final WatercolorStyle style;

  const LuxuryWatercolorBackground({
    super.key,
    required this.child,
    this.colors,
    this.animated = true,
    this.animationDuration = const Duration(seconds: 8),
    this.style = WatercolorStyle.elegant,
  });

  @override
  State<LuxuryWatercolorBackground> createState() => _LuxuryWatercolorBackgroundState();
}

enum WatercolorStyle {
  elegant,    // Jemné přechody jako v reference obrázku
  dreamy,     // V<PERSON>ce rozmazané efekty
  coastal,    // Pobřežní téma
  luxury,     // Zlaté akcenty
}

class _LuxuryWatercolorBackgroundState extends State<LuxuryWatercolorBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOutSine),
    );

    if (widget.animated) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: _createWatercolorGradient(),
          ),
          child: Stack(
            children: [
              // Základní watercolor vrstva
              _buildWatercolorLayer(),
              // Jemné textury
              _buildTextureOverlay(),
              // Obsah
              widget.child,
            ],
          ),
        );
      },
    );
  }

  /// Vytvoří hlavní watercolor gradient podle stylu
  LinearGradient _createWatercolorGradient() {
    final colors = widget.colors ?? _getDefaultColors();
    final animationValue = widget.animated ? _animation.value : 0.0;

    // Animované pozice pro gradient
    final begin = Alignment.lerp(
      Alignment.topLeft,
      Alignment.topRight,
      (math.sin(animationValue * math.pi * 2) + 1) / 4,
    )!;

    final end = Alignment.lerp(
      Alignment.bottomRight,
      Alignment.bottomLeft,
      (math.cos(animationValue * math.pi * 2) + 1) / 4,
    )!;

    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
      stops: _createWatercolorStops(colors.length),
    );
  }

  /// Vytvoří watercolor vrstvu s jemnými efekty
  Widget _buildWatercolorLayer() {
    return Positioned.fill(
      child: CustomPaint(
        painter: WatercolorPainter(
          animationValue: widget.animated ? _animation.value : 0.0,
          style: widget.style,
          colors: widget.colors ?? _getDefaultColors(),
        ),
      ),
    );
  }

  /// Vytvoří jemný texture overlay
  Widget _buildTextureOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.5,
            colors: [
              Colors.white.withValues(alpha: 0.1),
              Colors.white.withValues(alpha: 0.05),
              Colors.transparent,
            ],
            stops: const [0.0, 0.7, 1.0],
          ),
        ),
      ),
    );
  }

  /// Získá výchozí barvy podle stylu
  List<Color> _getDefaultColors() {
    switch (widget.style) {
      case WatercolorStyle.elegant:
        return WatercolorTheme.adriaticDreamGradient;
      case WatercolorStyle.dreamy:
        return WatercolorTheme.coastalMistGradient;
      case WatercolorStyle.coastal:
        return WatercolorTheme.elegantTealGradient;
      case WatercolorStyle.luxury:
        return WatercolorTheme.luxuryGoldGradient;
    }
  }

  /// Vytvoří watercolor stops pro přirozený vzhled
  List<double> _createWatercolorStops(int colorCount) {
    if (colorCount <= 1) return [0.0];

    final stops = <double>[];
    for (int i = 0; i < colorCount; i++) {
      // Nelineární distribuce pro watercolor efekt
      final linear = i / (colorCount - 1);
      final curved = math.pow(linear, 0.8).toDouble();
      stops.add(curved);
    }
    return stops;
  }
}

/// Custom painter pro watercolor efekty
class WatercolorPainter extends CustomPainter {
  final double animationValue;
  final WatercolorStyle style;
  final List<Color> colors;

  WatercolorPainter({
    required this.animationValue,
    required this.style,
    required this.colors,
  });

  @override
  void paint(Canvas canvas, Size size) {
    _paintWatercolorBlobs(canvas, size);
    _paintSoftTextures(canvas, size);
  }

  /// Maluje watercolor skvrny
  void _paintWatercolorBlobs(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.multiply;

    // Různé velikosti a pozice skvrn
    final blobs = [
      _WatercolorBlob(
        center: Offset(size.width * 0.2, size.height * 0.3),
        radius: size.width * 0.4,
        color: colors[0].withValues(alpha: 0.1),
      ),
      _WatercolorBlob(
        center: Offset(size.width * 0.8, size.height * 0.7),
        radius: size.width * 0.3,
        color: colors.length > 1 ? colors[1].withValues(alpha: 0.08) : colors[0].withValues(alpha: 0.08),
      ),
      _WatercolorBlob(
        center: Offset(size.width * 0.6, size.height * 0.2),
        radius: size.width * 0.25,
        color: colors.length > 2 ? colors[2].withValues(alpha: 0.06) : colors[0].withValues(alpha: 0.06),
      ),
    ];

    for (final blob in blobs) {
      paint.color = blob.color;
      
      // Animované pozice
      final animatedCenter = Offset(
        blob.center.dx + math.sin(animationValue * math.pi * 2) * 20,
        blob.center.dy + math.cos(animationValue * math.pi * 2) * 15,
      );

      canvas.drawCircle(animatedCenter, blob.radius, paint);
    }
  }

  /// Maluje jemné textury
  void _paintSoftTextures(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.overlay;

    // Jemné kruhy pro texture
    for (int i = 0; i < 8; i++) {
      final angle = (i / 8) * math.pi * 2 + animationValue * math.pi;
      final radius = size.width * (0.1 + (i % 3) * 0.05);
      final center = Offset(
        size.width * 0.5 + math.cos(angle) * size.width * 0.3,
        size.height * 0.5 + math.sin(angle) * size.height * 0.3,
      );

      paint.color = Colors.white.withValues(alpha: 0.02);
      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(WatercolorPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}

/// Pomocná třída pro watercolor skvrny
class _WatercolorBlob {
  final Offset center;
  final double radius;
  final Color color;

  _WatercolorBlob({
    required this.center,
    required this.radius,
    required this.color,
  });
}

/// Watercolor karta s luxusním designem
class LuxuryWatercolorCard extends StatelessWidget {
  final Widget child;
  final List<Color>? colors;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? shadows;
  final WatercolorStyle style;

  const LuxuryWatercolorCard({
    super.key,
    required this.child,
    this.colors,
    this.padding,
    this.borderRadius,
    this.shadows,
    this.style = WatercolorStyle.elegant,
  });

  @override
  Widget build(BuildContext context) {
    final cardColors = colors ?? _getCardColors();
    
    return Container(
      padding: padding ?? const EdgeInsets.all(WatercolorTheme.spacingM),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: cardColors,
          stops: const [0.0, 0.6, 1.0],
        ),
        borderRadius: borderRadius ?? WatercolorTheme.mediumRadius,
        boxShadow: shadows ?? _getLuxuryShadows(),
      ),
      child: child,
    );
  }

  List<Color> _getCardColors() {
    switch (style) {
      case WatercolorStyle.elegant:
        return [
          WatercolorTheme.pureWhite,
          WatercolorTheme.coastalMist.withValues(alpha: 0.3),
          WatercolorTheme.warmCream.withValues(alpha: 0.5),
        ];
      case WatercolorStyle.dreamy:
        return [
          WatercolorTheme.warmCream,
          WatercolorTheme.softPeach.withValues(alpha: 0.4),
          WatercolorTheme.pureWhite,
        ];
      case WatercolorStyle.coastal:
        return [
          WatercolorTheme.pureWhite,
          WatercolorTheme.elegantTeal.withValues(alpha: 0.1),
          WatercolorTheme.coastalMist.withValues(alpha: 0.3),
        ];
      case WatercolorStyle.luxury:
        return [
          WatercolorTheme.warmCream,
          WatercolorTheme.luxuryGold.withValues(alpha: 0.1),
          WatercolorTheme.softPeach.withValues(alpha: 0.2),
        ];
    }
  }

  List<BoxShadow> _getLuxuryShadows() {
    return [
      BoxShadow(
        color: WatercolorTheme.deepNavy.withValues(alpha: 0.08),
        blurRadius: 20,
        offset: const Offset(0, 8),
        spreadRadius: 0,
      ),
      BoxShadow(
        color: WatercolorTheme.elegantTeal.withValues(alpha: 0.04),
        blurRadius: 40,
        offset: const Offset(0, 16),
        spreadRadius: 0,
      ),
    ];
  }
}
