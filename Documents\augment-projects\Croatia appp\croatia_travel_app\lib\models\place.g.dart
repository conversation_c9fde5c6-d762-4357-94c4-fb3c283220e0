// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'place.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Place _$PlaceFromJson(Map<String, dynamic> json) => Place(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      excerpt: json['excerpt'] as String?,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      region: json['region'] as String,
      type: $enumDecode(_$PlaceTypeEnumMap, json['type']),
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      isVisited: json['isVisited'] as bool? ?? false,
      visitedDate: json['visitedDate'] == null
          ? null
          : DateTime.parse(json['visitedDate'] as String),
      userNotes: json['userNotes'] as String?,
      rating: (json['rating'] as num?)?.toDouble(),
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PlaceToJson(Place instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'excerpt': instance.excerpt,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'region': instance.region,
      'type': _$PlaceTypeEnumMap[instance.type]!,
      'images': instance.images,
      'isVisited': instance.isVisited,
      'visitedDate': instance.visitedDate?.toIso8601String(),
      'userNotes': instance.userNotes,
      'rating': instance.rating,
      'tags': instance.tags,
      'additionalInfo': instance.additionalInfo,
    };

const _$PlaceTypeEnumMap = {
  PlaceType.monument: 'monument',
  PlaceType.beach: 'beach',
  PlaceType.restaurant: 'restaurant',
  PlaceType.hotel: 'hotel',
  PlaceType.museum: 'museum',
  PlaceType.park: 'park',
  PlaceType.church: 'church',
  PlaceType.castle: 'castle',
  PlaceType.viewpoint: 'viewpoint',
  PlaceType.other: 'other',
};
