import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_paypal_payment/flutter_paypal_payment.dart';
import 'package:http/http.dart' as http;

/// 💰 PAYPAL PAYMENT SERVICE - PayPal payment processing
class PayPalPaymentService {
  static final PayPalPaymentService _instance =
      PayPalPaymentService._internal();
  factory PayPalPaymentService() => _instance;
  PayPalPaymentService._internal();

  bool _isInitialized = false;
  final StreamController<PayPalPaymentEvent> _eventController =
      StreamController.broadcast();

  // PayPal configuration
  static const String _clientId =
      'YOUR_PAYPAL_CLIENT_ID_HERE'; // REPLACE WITH YOUR CLIENT ID
  static const String _clientSecret =
      'YOUR_PAYPAL_CLIENT_SECRET_HERE'; // REPLACE WITH YOUR SECRET
  static const bool _sandboxMode = true; // Set to false in production

  /// Stream PayPal payment událostí
  Stream<PayPalPaymentEvent> get paymentEvents => _eventController.stream;

  /// Inicializace PayPal
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('💰 Inicializuji PayPal Payment Service...');

      // PayPal SDK se inicializuje při každé platbě
      _isInitialized = true;
      debugPrint('✅ PayPal Payment Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci PayPal: $e');
      rethrow;
    }
  }

  /// Zpracování PayPal platby
  Future<PayPalPaymentResult> processPayPalPayment({
    required double amount,
    required String currency,
    required String description,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      debugPrint('💰 Zpracovávám PayPal platbu: $amount $currency');

      // Konfigurace PayPal platby
      final transactions = [
        {
          "amount": {
            "total": amount.toStringAsFixed(2),
            "currency": currency.toUpperCase(),
            "details": {
              "subtotal": amount.toStringAsFixed(2),
              "tax": "0",
              "shipping": "0",
              "handling_fee": "0",
              "shipping_discount": "0",
              "insurance": "0",
            },
          },
          "description": description,
          "item_list": {
            "items": [
              {
                "name": description,
                "quantity": 1,
                "price": amount.toStringAsFixed(2),
                "currency": currency.toUpperCase(),
              },
            ],
          },
        },
      ];

      // Spuštění PayPal platby
      final result = await Navigator.of(navigatorKey.currentContext!).push(
        MaterialPageRoute(
          builder: (BuildContext context) => PaypalCheckoutView(
            sandboxMode: _sandboxMode,
            clientId: _clientId,
            secretKey: _clientSecret,
            transactions: transactions,
            note: "Platba za Croatia Travel App služby",
            onSuccess: (Map params) async {
              debugPrint("✅ PayPal platba úspěšná: $params");

              _eventController.add(
                PayPalPaymentEvent(
                  type: PayPalEventType.paymentSucceeded,
                  paymentId: params['paymentId'],
                  amount: amount,
                  timestamp: DateTime.now(),
                  details: Map<String, dynamic>.from(params),
                ),
              );
            },
            onError: (error) {
              debugPrint("❌ PayPal platba selhala: $error");

              _eventController.add(
                PayPalPaymentEvent(
                  type: PayPalEventType.paymentFailed,
                  error: error.toString(),
                  timestamp: DateTime.now(),
                ),
              );
            },
            onCancel: () {
              debugPrint("🚫 PayPal platba zrušena");

              _eventController.add(
                PayPalPaymentEvent(
                  type: PayPalEventType.paymentCancelled,
                  timestamp: DateTime.now(),
                ),
              );
            },
          ),
        ),
      );

      if (result != null && result['paymentId'] != null) {
        return PayPalPaymentResult(
          success: true,
          paymentId: result['paymentId'],
          amount: amount,
          currency: currency,
          description: description,
          timestamp: DateTime.now(),
          details: result,
        );
      } else {
        return PayPalPaymentResult(
          success: false,
          error: 'Payment was cancelled or failed',
          timestamp: DateTime.now(),
        );
      }
    } catch (e) {
      debugPrint('❌ Chyba při PayPal platbě: $e');

      final result = PayPalPaymentResult(
        success: false,
        error: e.toString(),
        timestamp: DateTime.now(),
      );

      _eventController.add(
        PayPalPaymentEvent(
          type: PayPalEventType.paymentFailed,
          error: e.toString(),
          timestamp: DateTime.now(),
        ),
      );

      return result;
    }
  }

  /// Získání PayPal access token
  Future<String?> getAccessToken() async {
    try {
      final String baseUrl = _sandboxMode
          ? 'https://api.sandbox.paypal.com'
          : 'https://api.paypal.com';

      final credentials = base64Encode(
        utf8.encode('$_clientId:$_clientSecret'),
      );

      final response = await http.post(
        Uri.parse('$baseUrl/v1/oauth2/token'),
        headers: {
          'Authorization': 'Basic $credentials',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'grant_type=client_credentials',
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['access_token'];
      } else {
        throw Exception('Failed to get access token: ${response.body}');
      }
    } catch (e) {
      debugPrint('❌ Chyba při získávání access token: $e');
      return null;
    }
  }

  /// Ověření PayPal platby
  Future<bool> verifyPayment(String paymentId) async {
    try {
      final accessToken = await getAccessToken();
      if (accessToken == null) return false;

      final String baseUrl = _sandboxMode
          ? 'https://api.sandbox.paypal.com'
          : 'https://api.paypal.com';

      final response = await http.get(
        Uri.parse('$baseUrl/v1/payments/payment/$paymentId'),
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['state'] == 'approved';
      } else {
        return false;
      }
    } catch (e) {
      debugPrint('❌ Chyba při ověřování PayPal platby: $e');
      return false;
    }
  }

  /// Refund PayPal platby
  Future<bool> refundPayment({
    required String paymentId,
    double? amount,
    String? currency,
    String? reason,
  }) async {
    try {
      final accessToken = await getAccessToken();
      if (accessToken == null) return false;

      final String baseUrl = _sandboxMode
          ? 'https://api.sandbox.paypal.com'
          : 'https://api.paypal.com';

      // Nejdříve získáme detaily platby
      final paymentResponse = await http.get(
        Uri.parse('$baseUrl/v1/payments/payment/$paymentId'),
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
      );

      if (paymentResponse.statusCode != 200) return false;

      final paymentData = jsonDecode(paymentResponse.body);
      final saleId =
          paymentData['transactions'][0]['related_resources'][0]['sale']['id'];

      // Provedeme refund
      final refundBody = <String, dynamic>{};
      if (amount != null && currency != null) {
        refundBody['amount'] = {
          'total': amount.toStringAsFixed(2),
          'currency': currency.toUpperCase(),
        };
      }
      if (reason != null) {
        refundBody['reason'] = reason;
      }

      final refundResponse = await http.post(
        Uri.parse('$baseUrl/v1/payments/sale/$saleId/refund'),
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(refundBody),
      );

      if (refundResponse.statusCode == 201) {
        _eventController.add(
          PayPalPaymentEvent(
            type: PayPalEventType.paymentRefunded,
            paymentId: paymentId,
            amount: amount,
            timestamp: DateTime.now(),
          ),
        );
        return true;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint('❌ Chyba při PayPal refund: $e');
      return false;
    }
  }

  /// Získání detailů PayPal platby
  Future<PayPalPaymentDetails?> getPaymentDetails(String paymentId) async {
    try {
      final accessToken = await getAccessToken();
      if (accessToken == null) return null;

      final String baseUrl = _sandboxMode
          ? 'https://api.sandbox.paypal.com'
          : 'https://api.paypal.com';

      final response = await http.get(
        Uri.parse('$baseUrl/v1/payments/payment/$paymentId'),
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return PayPalPaymentDetails.fromJson(data);
      } else {
        return null;
      }
    } catch (e) {
      debugPrint('❌ Chyba při získávání PayPal detailů: $e');
      return null;
    }
  }

  /// Vytvoření PayPal subscription
  Future<String?> createSubscription({
    required String planId,
    required Map<String, dynamic> subscriber,
  }) async {
    try {
      final accessToken = await getAccessToken();
      if (accessToken == null) return null;

      final String baseUrl = _sandboxMode
          ? 'https://api.sandbox.paypal.com'
          : 'https://api.paypal.com';

      final subscriptionData = {
        'plan_id': planId,
        'subscriber': subscriber,
        'application_context': {
          'brand_name': 'Croatia Travel App',
          'locale': 'hr-HR',
          'shipping_preference': 'NO_SHIPPING',
          'user_action': 'SUBSCRIBE_NOW',
          'payment_method': {
            'payer_selected': 'PAYPAL',
            'payee_preferred': 'IMMEDIATE_PAYMENT_REQUIRED',
          },
          'return_url': 'https://croatia-travel-app.com/success',
          'cancel_url': 'https://croatia-travel-app.com/cancel',
        },
      };

      final response = await http.post(
        Uri.parse('$baseUrl/v1/billing/subscriptions'),
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Prefer': 'return=representation',
        },
        body: jsonEncode(subscriptionData),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return data['id'];
      } else {
        return null;
      }
    } catch (e) {
      debugPrint('❌ Chyba při vytváření PayPal subscription: $e');
      return null;
    }
  }

  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  bool get isSandboxMode => _sandboxMode;
}

/// PayPal Payment Result Model
class PayPalPaymentResult {
  final bool success;
  final String? paymentId;
  final double? amount;
  final String? currency;
  final String? description;
  final String? error;
  final DateTime timestamp;
  final Map<String, dynamic>? details;

  PayPalPaymentResult({
    required this.success,
    this.paymentId,
    this.amount,
    this.currency,
    this.description,
    this.error,
    required this.timestamp,
    this.details,
  });
}

/// PayPal Payment Event Model
class PayPalPaymentEvent {
  final PayPalEventType type;
  final String? paymentId;
  final double? amount;
  final String? error;
  final DateTime timestamp;
  final Map<String, dynamic>? details;

  PayPalPaymentEvent({
    required this.type,
    this.paymentId,
    this.amount,
    this.error,
    required this.timestamp,
    this.details,
  });
}

enum PayPalEventType {
  paymentSucceeded,
  paymentFailed,
  paymentCancelled,
  paymentRefunded,
}

/// PayPal Payment Details Model
class PayPalPaymentDetails {
  final String id;
  final String state;
  final double amount;
  final String currency;
  final DateTime createTime;
  final String? description;
  final Map<String, dynamic>? payer;

  PayPalPaymentDetails({
    required this.id,
    required this.state,
    required this.amount,
    required this.currency,
    required this.createTime,
    this.description,
    this.payer,
  });

  factory PayPalPaymentDetails.fromJson(Map<String, dynamic> json) {
    final transaction = json['transactions'][0];
    final amount = double.parse(transaction['amount']['total']);

    return PayPalPaymentDetails(
      id: json['id'],
      state: json['state'],
      amount: amount,
      currency: transaction['amount']['currency'],
      createTime: DateTime.parse(json['create_time']),
      description: transaction['description'],
      payer: json['payer'],
    );
  }
}

// Global navigator key pro PayPal navigation
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
