// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'b2b_sales.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

B2BLead _$B2BLeadFromJson(Map<String, dynamic> json) => B2BLead(
      id: json['id'] as String,
      companyName: json['companyName'] as String,
      contactPerson: json['contactPerson'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      segment: $enumDecode(_$B2BSegmentEnumMap, json['segment']),
      source: $enumDecode(_$LeadSourceEnumMap, json['source']),
      status: $enumDecode(_$LeadStatusEnumMap, json['status']),
      estimatedValue: (json['estimatedValue'] as num).toDouble(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastContactAt: json['lastContactAt'] == null
          ? null
          : DateTime.parse(json['lastContactAt'] as String),
      notes: json['notes'] as String?,
      urgency: $enumDecodeNullable(_$UrgencyLevelEnumMap, json['urgency']) ??
          UrgencyLevel.medium,
      customFields: json['customFields'] as Map<String, dynamic>? ?? const {},
      qualification: json['qualification'] == null
          ? null
          : LeadQualification.fromJson(
              json['qualification'] as Map<String, dynamic>),
      score: (json['score'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$B2BLeadToJson(B2BLead instance) => <String, dynamic>{
      'id': instance.id,
      'companyName': instance.companyName,
      'contactPerson': instance.contactPerson,
      'email': instance.email,
      'phone': instance.phone,
      'segment': _$B2BSegmentEnumMap[instance.segment]!,
      'source': _$LeadSourceEnumMap[instance.source]!,
      'status': _$LeadStatusEnumMap[instance.status]!,
      'estimatedValue': instance.estimatedValue,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastContactAt': instance.lastContactAt?.toIso8601String(),
      'notes': instance.notes,
      'urgency': _$UrgencyLevelEnumMap[instance.urgency]!,
      'customFields': instance.customFields,
      'qualification': instance.qualification,
      'score': instance.score,
    };

const _$B2BSegmentEnumMap = {
  B2BSegment.startup: 'startup',
  B2BSegment.smallBusiness: 'smallBusiness',
  B2BSegment.mediumBusiness: 'mediumBusiness',
  B2BSegment.enterprise: 'enterprise',
  B2BSegment.government: 'government',
  B2BSegment.tourismBoard: 'tourismBoard',
  B2BSegment.travelAgency: 'travelAgency',
  B2BSegment.hotel: 'hotel',
  B2BSegment.restaurant: 'restaurant',
  B2BSegment.eventOrganizer: 'eventOrganizer',
};

const _$LeadSourceEnumMap = {
  LeadSource.website: 'website',
  LeadSource.referral: 'referral',
  LeadSource.coldOutreach: 'coldOutreach',
  LeadSource.socialMedia: 'socialMedia',
  LeadSource.event: 'event',
  LeadSource.partnership: 'partnership',
  LeadSource.advertising: 'advertising',
  LeadSource.inbound: 'inbound',
};

const _$LeadStatusEnumMap = {
  LeadStatus.new_: 'new_',
  LeadStatus.contacted: 'contacted',
  LeadStatus.qualified: 'qualified',
  LeadStatus.proposal: 'proposal',
  LeadStatus.negotiation: 'negotiation',
  LeadStatus.won: 'won',
  LeadStatus.lost: 'lost',
  LeadStatus.disqualified: 'disqualified',
  LeadStatus.closed: 'closed',
};

const _$UrgencyLevelEnumMap = {
  UrgencyLevel.low: 'low',
  UrgencyLevel.medium: 'medium',
  UrgencyLevel.high: 'high',
  UrgencyLevel.critical: 'critical',
};

B2BProposal _$B2BProposalFromJson(Map<String, dynamic> json) => B2BProposal(
      id: json['id'] as String,
      leadId: json['leadId'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      totalValue: (json['totalValue'] as num).toDouble(),
      package: B2BPackage.fromJson(json['package'] as Map<String, dynamic>),
      status: $enumDecode(_$ProposalStatusEnumMap, json['status']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      sentAt: json['sentAt'] == null
          ? null
          : DateTime.parse(json['sentAt'] as String),
      validUntil: json['validUntil'] == null
          ? null
          : DateTime.parse(json['validUntil'] as String),
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => ProposalItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      terms: json['terms'] as String?,
      companyName: json['companyName'] as String?,
      customPrice: (json['customPrice'] as num?)?.toDouble(),
      contractLength: (json['contractLength'] as num?)?.toInt(),
    );

Map<String, dynamic> _$B2BProposalToJson(B2BProposal instance) =>
    <String, dynamic>{
      'id': instance.id,
      'leadId': instance.leadId,
      'title': instance.title,
      'description': instance.description,
      'totalValue': instance.totalValue,
      'package': instance.package,
      'status': _$ProposalStatusEnumMap[instance.status]!,
      'createdAt': instance.createdAt.toIso8601String(),
      'sentAt': instance.sentAt?.toIso8601String(),
      'validUntil': instance.validUntil?.toIso8601String(),
      'items': instance.items,
      'terms': instance.terms,
      'companyName': instance.companyName,
      'customPrice': instance.customPrice,
      'contractLength': instance.contractLength,
    };

const _$ProposalStatusEnumMap = {
  ProposalStatus.draft: 'draft',
  ProposalStatus.sent: 'sent',
  ProposalStatus.viewed: 'viewed',
  ProposalStatus.accepted: 'accepted',
  ProposalStatus.rejected: 'rejected',
  ProposalStatus.expired: 'expired',
};

B2BContract _$B2BContractFromJson(Map<String, dynamic> json) => B2BContract(
      id: json['id'] as String,
      proposalId: json['proposalId'] as String,
      clientId: json['clientId'] as String,
      title: json['title'] as String,
      value: (json['value'] as num).toDouble(),
      status: $enumDecode(_$ContractStatusEnumMap, json['status']),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      paymentTerms:
          PaymentTerms.fromJson(json['paymentTerms'] as Map<String, dynamic>),
      milestones: (json['milestones'] as List<dynamic>?)
              ?.map(
                  (e) => ContractMilestone.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      signedDocumentUrl: json['signedDocumentUrl'] as String?,
    );

Map<String, dynamic> _$B2BContractToJson(B2BContract instance) =>
    <String, dynamic>{
      'id': instance.id,
      'proposalId': instance.proposalId,
      'clientId': instance.clientId,
      'title': instance.title,
      'value': instance.value,
      'status': _$ContractStatusEnumMap[instance.status]!,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'paymentTerms': instance.paymentTerms,
      'milestones': instance.milestones,
      'signedDocumentUrl': instance.signedDocumentUrl,
    };

const _$ContractStatusEnumMap = {
  ContractStatus.draft: 'draft',
  ContractStatus.active: 'active',
  ContractStatus.completed: 'completed',
  ContractStatus.cancelled: 'cancelled',
  ContractStatus.suspended: 'suspended',
};

B2BClient _$B2BClientFromJson(Map<String, dynamic> json) => B2BClient(
      id: json['id'] as String,
      companyName: json['companyName'] as String,
      industry: json['industry'] as String,
      employeeCount: (json['employeeCount'] as num).toInt(),
      annualRevenue: (json['annualRevenue'] as num).toDouble(),
      primaryContact: json['primaryContact'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      address: json['address'] as String,
      tier: $enumDecode(_$ClientTierEnumMap, json['tier']),
      onboardedAt: DateTime.parse(json['onboardedAt'] as String),
      contractIds: (json['contractIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$B2BClientToJson(B2BClient instance) => <String, dynamic>{
      'id': instance.id,
      'companyName': instance.companyName,
      'industry': instance.industry,
      'employeeCount': instance.employeeCount,
      'annualRevenue': instance.annualRevenue,
      'primaryContact': instance.primaryContact,
      'email': instance.email,
      'phone': instance.phone,
      'address': instance.address,
      'tier': _$ClientTierEnumMap[instance.tier]!,
      'onboardedAt': instance.onboardedAt.toIso8601String(),
      'contractIds': instance.contractIds,
    };

const _$ClientTierEnumMap = {
  ClientTier.bronze: 'bronze',
  ClientTier.silver: 'silver',
  ClientTier.gold: 'gold',
  ClientTier.platinum: 'platinum',
};

SalesMetrics _$SalesMetricsFromJson(Map<String, dynamic> json) => SalesMetrics(
      totalRevenue: (json['totalRevenue'] as num).toDouble(),
      totalLeads: (json['totalLeads'] as num).toInt(),
      convertedLeads: (json['convertedLeads'] as num).toInt(),
      conversionRate: (json['conversionRate'] as num).toDouble(),
      averageDealSize: (json['averageDealSize'] as num).toDouble(),
      activePipeline: (json['activePipeline'] as num).toInt(),
      revenueBySegment: (json['revenueBySegment'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      leadsBySource: Map<String, int>.from(json['leadsBySource'] as Map),
      periodStart: DateTime.parse(json['periodStart'] as String),
      periodEnd: DateTime.parse(json['periodEnd'] as String),
    );

Map<String, dynamic> _$SalesMetricsToJson(SalesMetrics instance) =>
    <String, dynamic>{
      'totalRevenue': instance.totalRevenue,
      'totalLeads': instance.totalLeads,
      'convertedLeads': instance.convertedLeads,
      'conversionRate': instance.conversionRate,
      'averageDealSize': instance.averageDealSize,
      'activePipeline': instance.activePipeline,
      'revenueBySegment': instance.revenueBySegment,
      'leadsBySource': instance.leadsBySource,
      'periodStart': instance.periodStart.toIso8601String(),
      'periodEnd': instance.periodEnd.toIso8601String(),
    };

SalesEvent _$SalesEventFromJson(Map<String, dynamic> json) => SalesEvent(
      id: json['id'] as String,
      type: $enumDecode(_$SalesEventTypeEnumMap, json['type']),
      leadId: json['leadId'] as String,
      description: json['description'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      userId: json['userId'] as String,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$SalesEventToJson(SalesEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$SalesEventTypeEnumMap[instance.type]!,
      'leadId': instance.leadId,
      'description': instance.description,
      'timestamp': instance.timestamp.toIso8601String(),
      'userId': instance.userId,
      'metadata': instance.metadata,
    };

const _$SalesEventTypeEnumMap = {
  SalesEventType.leadCreated: 'leadCreated',
  SalesEventType.leadContacted: 'leadContacted',
  SalesEventType.proposalSent: 'proposalSent',
  SalesEventType.contractSigned: 'contractSigned',
  SalesEventType.meetingScheduled: 'meetingScheduled',
  SalesEventType.followUpRequired: 'followUpRequired',
  SalesEventType.leadQualified: 'leadQualified',
  SalesEventType.leadDisqualified: 'leadDisqualified',
  SalesEventType.proposalCreated: 'proposalCreated',
  SalesEventType.dealClosed: 'dealClosed',
};

B2BPackage _$B2BPackageFromJson(Map<String, dynamic> json) => B2BPackage(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      features:
          (json['features'] as List<dynamic>).map((e) => e as String).toList(),
      monthlyPrice: (json['monthlyPrice'] as num).toDouble(),
    );

Map<String, dynamic> _$B2BPackageToJson(B2BPackage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'price': instance.price,
      'features': instance.features,
      'monthlyPrice': instance.monthlyPrice,
    };

ProposalItem _$ProposalItemFromJson(Map<String, dynamic> json) => ProposalItem(
      name: json['name'] as String,
      description: json['description'] as String,
      quantity: (json['quantity'] as num).toInt(),
      unitPrice: (json['unitPrice'] as num).toDouble(),
      totalPrice: (json['totalPrice'] as num).toDouble(),
    );

Map<String, dynamic> _$ProposalItemToJson(ProposalItem instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'quantity': instance.quantity,
      'unitPrice': instance.unitPrice,
      'totalPrice': instance.totalPrice,
    };

PaymentTerms _$PaymentTermsFromJson(Map<String, dynamic> json) => PaymentTerms(
      paymentDays: (json['paymentDays'] as num).toInt(),
      currency: json['currency'] as String,
      method: $enumDecode(_$PaymentMethodEnumMap, json['method']),
      isRecurring: json['isRecurring'] as bool,
      recurringMonths: (json['recurringMonths'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PaymentTermsToJson(PaymentTerms instance) =>
    <String, dynamic>{
      'paymentDays': instance.paymentDays,
      'currency': instance.currency,
      'method': _$PaymentMethodEnumMap[instance.method]!,
      'isRecurring': instance.isRecurring,
      'recurringMonths': instance.recurringMonths,
    };

const _$PaymentMethodEnumMap = {
  PaymentMethod.bankTransfer: 'bankTransfer',
  PaymentMethod.creditCard: 'creditCard',
  PaymentMethod.paypal: 'paypal',
  PaymentMethod.crypto: 'crypto',
  PaymentMethod.check: 'check',
};

ContractMilestone _$ContractMilestoneFromJson(Map<String, dynamic> json) =>
    ContractMilestone(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      dueDate: DateTime.parse(json['dueDate'] as String),
      value: (json['value'] as num).toDouble(),
      isCompleted: json['isCompleted'] as bool,
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
    );

Map<String, dynamic> _$ContractMilestoneToJson(ContractMilestone instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'dueDate': instance.dueDate.toIso8601String(),
      'value': instance.value,
      'isCompleted': instance.isCompleted,
      'completedAt': instance.completedAt?.toIso8601String(),
    };

LeadQualification _$LeadQualificationFromJson(Map<String, dynamic> json) =>
    LeadQualification(
      leadId: json['leadId'] as String,
      budgetScore: (json['budgetScore'] as num).toInt(),
      authorityScore: (json['authorityScore'] as num).toInt(),
      needScore: (json['needScore'] as num).toInt(),
      timelineScore: (json['timelineScore'] as num).toInt(),
      totalScore: (json['totalScore'] as num).toInt(),
      isQualified: json['isQualified'] as bool,
      notes: json['notes'] as String?,
      budget: (json['budget'] as num?)?.toDouble(),
      employeeCount: (json['employeeCount'] as num?)?.toInt(),
      hasDecisionMaker: json['hasDecisionMaker'] as bool?,
      urgency: $enumDecodeNullable(_$UrgencyLevelEnumMap, json['urgency']),
      qualifiedAt: json['qualifiedAt'] == null
          ? null
          : DateTime.parse(json['qualifiedAt'] as String),
      qualifiedBy: json['qualifiedBy'] as String?,
    );

Map<String, dynamic> _$LeadQualificationToJson(LeadQualification instance) =>
    <String, dynamic>{
      'leadId': instance.leadId,
      'budgetScore': instance.budgetScore,
      'authorityScore': instance.authorityScore,
      'needScore': instance.needScore,
      'timelineScore': instance.timelineScore,
      'totalScore': instance.totalScore,
      'isQualified': instance.isQualified,
      'notes': instance.notes,
      'budget': instance.budget,
      'employeeCount': instance.employeeCount,
      'hasDecisionMaker': instance.hasDecisionMaker,
      'urgency': _$UrgencyLevelEnumMap[instance.urgency],
      'qualifiedAt': instance.qualifiedAt?.toIso8601String(),
      'qualifiedBy': instance.qualifiedBy,
    };

SalesDashboard _$SalesDashboardFromJson(Map<String, dynamic> json) =>
    SalesDashboard(
      generatedAt: DateTime.parse(json['generatedAt'] as String),
      period: $enumDecode(_$ReportPeriodEnumMap, json['period']),
      totalRevenue: (json['totalRevenue'] as num).toDouble(),
      totalLeads: (json['totalLeads'] as num).toInt(),
      convertedLeads: (json['convertedLeads'] as num).toInt(),
      conversionRate: (json['conversionRate'] as num).toDouble(),
      activeProposals: (json['activeProposals'] as num).toInt(),
      wonDeals: (json['wonDeals'] as num).toInt(),
      lostDeals: (json['lostDeals'] as num).toInt(),
      averageDealSize: (json['averageDealSize'] as num).toDouble(),
      revenueBySegment: (json['revenueBySegment'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      leadsBySource: Map<String, int>.from(json['leadsBySource'] as Map),
      topLeads: (json['topLeads'] as List<dynamic>)
          .map((e) => B2BLead.fromJson(e as Map<String, dynamic>))
          .toList(),
      recentProposals: (json['recentProposals'] as List<dynamic>)
          .map((e) => B2BProposal.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SalesDashboardToJson(SalesDashboard instance) =>
    <String, dynamic>{
      'generatedAt': instance.generatedAt.toIso8601String(),
      'period': _$ReportPeriodEnumMap[instance.period]!,
      'totalRevenue': instance.totalRevenue,
      'totalLeads': instance.totalLeads,
      'convertedLeads': instance.convertedLeads,
      'conversionRate': instance.conversionRate,
      'activeProposals': instance.activeProposals,
      'wonDeals': instance.wonDeals,
      'lostDeals': instance.lostDeals,
      'averageDealSize': instance.averageDealSize,
      'revenueBySegment': instance.revenueBySegment,
      'leadsBySource': instance.leadsBySource,
      'topLeads': instance.topLeads,
      'recentProposals': instance.recentProposals,
    };

const _$ReportPeriodEnumMap = {
  ReportPeriod.daily: 'daily',
  ReportPeriod.weekly: 'weekly',
  ReportPeriod.monthly: 'monthly',
  ReportPeriod.quarterly: 'quarterly',
  ReportPeriod.yearly: 'yearly',
  ReportPeriod.custom: 'custom',
  ReportPeriod.thisMonth: 'thisMonth',
  ReportPeriod.lastMonth: 'lastMonth',
  ReportPeriod.thisQuarter: 'thisQuarter',
  ReportPeriod.thisYear: 'thisYear',
};

SalesReport _$SalesReportFromJson(Map<String, dynamic> json) => SalesReport(
      id: json['id'] as String,
      period: $enumDecode(_$ReportPeriodEnumMap, json['period']),
      fromDate: DateTime.parse(json['fromDate'] as String),
      toDate: DateTime.parse(json['toDate'] as String),
      metrics: (json['metrics'] as List<dynamic>)
          .map((e) => $enumDecode(_$SalesMetricTypeEnumMap, e))
          .toList(),
      dashboard:
          SalesDashboard.fromJson(json['dashboard'] as Map<String, dynamic>),
      insights:
          (json['insights'] as List<dynamic>).map((e) => e as String).toList(),
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      generatedAt: DateTime.parse(json['generatedAt'] as String),
    );

Map<String, dynamic> _$SalesReportToJson(SalesReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'period': _$ReportPeriodEnumMap[instance.period]!,
      'fromDate': instance.fromDate.toIso8601String(),
      'toDate': instance.toDate.toIso8601String(),
      'metrics':
          instance.metrics.map((e) => _$SalesMetricTypeEnumMap[e]!).toList(),
      'dashboard': instance.dashboard,
      'insights': instance.insights,
      'recommendations': instance.recommendations,
      'generatedAt': instance.generatedAt.toIso8601String(),
    };

const _$SalesMetricTypeEnumMap = {
  SalesMetricType.revenue: 'revenue',
  SalesMetricType.leads: 'leads',
  SalesMetricType.conversion: 'conversion',
  SalesMetricType.deals: 'deals',
  SalesMetricType.pipeline: 'pipeline',
  SalesMetricType.forecast: 'forecast',
};

SalesForecast _$SalesForecastFromJson(Map<String, dynamic> json) =>
    SalesForecast(
      month: DateTime.parse(json['month'] as String),
      projectedRevenue: (json['projectedRevenue'] as num).toDouble(),
      confidence: (json['confidence'] as num).toDouble(),
      factors:
          (json['factors'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$SalesForecastToJson(SalesForecast instance) =>
    <String, dynamic>{
      'month': instance.month.toIso8601String(),
      'projectedRevenue': instance.projectedRevenue,
      'confidence': instance.confidence,
      'factors': instance.factors,
    };
