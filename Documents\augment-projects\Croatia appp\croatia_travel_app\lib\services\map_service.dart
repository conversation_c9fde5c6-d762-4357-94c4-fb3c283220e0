import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

import '../models/map_place.dart';
import '../models/ticket.dart';
import '../services/ticket_service.dart';

/// Služba pro správu map a lokalizace
class MapService extends ChangeNotifier {
  static final MapService _instance = MapService._internal();
  factory MapService() => _instance;
  MapService._internal();

  // Kontrolery a stav
  Completer<GoogleMapController>? _mapController;
  Position? _currentPosition;
  bool _isLoading = false;
  String? _error;

  // Data
  List<MapPlace> _allPlaces = [];
  List<MapPlace> _filteredPlaces = [];
  MapPlaceFilter _currentFilter = const MapPlaceFilter();

  // Služby pro načítání dat
  final TicketService _ticketService = TicketService();

  // Gettery
  List<MapPlace> get allPlaces => _allPlaces;
  List<MapPlace> get filteredPlaces => _filteredPlaces;
  MapPlaceFilter get currentFilter => _currentFilter;
  Position? get currentPosition => _currentPosition;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasLocationPermission => _currentPosition != null;

  // Chorvatské souřadnice pro výchozí pozici
  static const LatLng _croatiaCenter = LatLng(45.1, 15.2);
  static const double _defaultZoom = 7.0;

  /// Inicializuje mapovou službu
  Future<void> initialize() async {
    _setLoading(true);
    try {
      await _requestLocationPermission();
      await _loadAllPlaces();
      _applyFilter();
    } catch (e) {
      _setError('Chyba při inicializaci mapy: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Požádá o povolení lokalizace
  Future<void> _requestLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      if (permission == LocationPermission.whileInUse ||
          permission == LocationPermission.always) {
        await _getCurrentLocation();
      }
    } catch (e) {
      debugPrint('Chyba při získávání lokalizace: $e');
    }
  }

  /// Získá aktuální polohu uživatele
  Future<void> _getCurrentLocation() async {
    try {
      _currentPosition = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );
      notifyListeners();
    } catch (e) {
      debugPrint('Chyba při získávání aktuální polohy: $e');
    }
  }

  /// Načte všechna místa ze všech služeb
  Future<void> _loadAllPlaces() async {
    final List<MapPlace> places = [];

    try {
      // Načti vstupenky
      await _ticketService.initialize();
      final tickets = _ticketService.tickets;
      places.addAll(tickets.map(_convertTicketToMapPlace));

      // Přidej ukázková místa
      places.addAll(_createSamplePlaces());

      _allPlaces = places;
      debugPrint('Načteno ${_allPlaces.length} míst na mapu');
    } catch (e) {
      debugPrint('Chyba při načítání míst: $e');
      throw Exception('Nepodařilo se načíst mapová data');
    }
  }

  /// Vytvoří ukázková místa pro demonstraci
  List<MapPlace> _createSamplePlaces() {
    return [
      // Restaurace v Záhřebu
      MapPlace(
        id: 'restaurant_zagreb_1',
        name: 'Dubrovnik Put',
        description: 'Tradiční chorvatská kuchyně v srdci Záhřebu.',
        position: const LatLng(45.8150, 15.9819),
        type: MapPlaceType.restaurant,
        category: MapPlaceCategory.food,
        address: 'Ilica 1, Záhřeb',
        phoneNumber: '+385 1 234 5678',
        website: 'https://dubrovnikput.hr',
        rating: 4.5,
        reviewCount: 127,
        price: 25.0,
        priceRange: '20-30€',
        tags: ['chorvatská kuchyně', 'tradiční', 'centrum'],
        isVerified: true,
        isOpen: true,
        openingHours: '11:00 - 23:00',
      ),

      // Ubytování v Splitu
      MapPlace(
        id: 'accommodation_split_1',
        name: 'Hotel Park Split',
        description: 'Luxusní hotel s výhledem na moře.',
        position: const LatLng(43.5081, 16.4402),
        type: MapPlaceType.accommodation,
        category: MapPlaceCategory.stay,
        address: 'Hatzeov perivoj 3, Split',
        phoneNumber: '+385 21 406 400',
        website: 'https://hotelparksplit.hr',
        rating: 4.8,
        reviewCount: 89,
        price: 120.0,
        priceRange: '100-150€',
        tags: ['hotel', 'luxusní', 'výhled na moře'],
        isVerified: true,
        isOpen: true,
      ),

      // Kulturní místo v Dubrovníku
      MapPlace(
        id: 'cultural_dubrovnik_1',
        name: 'Stradun',
        description: 'Hlavní ulice starého města Dubrovníku.',
        position: const LatLng(42.6407, 18.1077),
        type: MapPlaceType.culturalSite,
        category: MapPlaceCategory.culture,
        address: 'Placa, Dubrovník',
        rating: 4.9,
        reviewCount: 234,
        tags: ['UNESCO', 'historické centrum', 'architektura'],
        isVerified: true,
        isOpen: true,
        openingHours: '24/7',
      ),

      // Pláž na Hvaru
      MapPlace(
        id: 'beach_hvar_1',
        name: 'Zlatni Rat',
        description: 'Nejznámější pláž v Chorvatsku na ostrově Brač.',
        position: const LatLng(43.2569, 16.6378),
        type: MapPlaceType.beach,
        category: MapPlaceCategory.nature,
        address: 'Bol, ostrov Brač',
        rating: 4.7,
        reviewCount: 156,
        tags: ['zlatá pláž', 'windsurfing', 'přírodní'],
        isVerified: true,
        isOpen: true,
      ),

      // Doprava v Záhřebu
      MapPlace(
        id: 'transport_zagreb_1',
        name: 'Hlavní nádraží Záhřeb',
        description: 'Centrální železniční stanice.',
        position: const LatLng(45.8058, 15.9777),
        type: MapPlaceType.transport,
        category: MapPlaceCategory.transport,
        address: 'Trg kralja Tomislava 12, Záhřeb',
        phoneNumber: '+385 1 378 2583',
        tags: ['vlak', 'doprava', 'centrum'],
        isVerified: true,
        isOpen: true,
        openingHours: '24/7',
      ),
    ];
  }

  /// Převede vstupenku na mapové místo
  MapPlace _convertTicketToMapPlace(Ticket ticket) {
    final adultPrice =
        ticket.pricing.categories
            .where((cat) => cat.category == TicketCategory.adult)
            .firstOrNull
            ?.price ??
        0.0;

    return MapPlace(
      id: 'ticket_${ticket.id}',
      name: ticket.title,
      description: ticket.description,
      position: LatLng(ticket.latitude ?? 0.0, ticket.longitude ?? 0.0),
      type: MapPlaceType.ticket,
      category: MapPlaceCategory.culture,
      address: ticket.location,
      website: ticket.provider.officialWebsite,
      phoneNumber: ticket.provider.phoneNumber,
      imageUrl: ticket.images.isNotEmpty ? ticket.images.first : null,
      rating: ticket.provider.rating,
      price: adultPrice,
      tags: ticket.tags,
      isVerified: ticket.provider.isVerified,
      isOpen: ticket.isActive,
      additionalData: {
        'type': ticket.type.toString(),
        'provider': ticket.provider.name,
        'hasGroupDiscounts': ticket.hasGroupDiscounts,
        'hasSeasonalDiscounts': ticket.hasSeasonalDiscounts,
        'features': ticket.features,
      },
    );
  }

  /// Aplikuje filtr na místa
  void _applyFilter() {
    _filteredPlaces = _allPlaces.where(_currentFilter.matches).toList();
    notifyListeners();
  }

  /// Nastaví nový filtr
  void setFilter(MapPlaceFilter filter) {
    _currentFilter = filter;
    _applyFilter();
  }

  /// Vymaže filtr
  void clearFilter() {
    _currentFilter = const MapPlaceFilter();
    _applyFilter();
  }

  /// Vyhledá místa podle dotazu
  void searchPlaces(String query) {
    final filter = _currentFilter.copyWith(searchQuery: query);
    setFilter(filter);
  }

  /// Filtruje podle typu místa
  void filterByType(Set<MapPlaceType> types) {
    final filter = _currentFilter.copyWith(types: types);
    setFilter(filter);
  }

  /// Filtruje podle kategorie
  void filterByCategory(Set<MapPlaceCategory> categories) {
    final filter = _currentFilter.copyWith(categories: categories);
    setFilter(filter);
  }

  /// Najde nejbližší místa k dané pozici
  List<MapPlace> findNearbyPlaces(LatLng position, {double radiusKm = 5.0}) {
    return _filteredPlaces.where((place) {
      final distance = _calculateDistance(position, place.position);
      return distance <= radiusKm;
    }).toList()..sort((a, b) {
      final distanceA = _calculateDistance(position, a.position);
      final distanceB = _calculateDistance(position, b.position);
      return distanceA.compareTo(distanceB);
    });
  }

  /// Spočítá vzdálenost mezi dvěma body v kilometrech
  double _calculateDistance(LatLng point1, LatLng point2) {
    return Geolocator.distanceBetween(
          point1.latitude,
          point1.longitude,
          point2.latitude,
          point2.longitude,
        ) /
        1000; // Převod na kilometry
  }

  /// Získá adresu z koordinátů
  Future<String> getAddressFromCoordinates(LatLng position) async {
    try {
      final placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        return '${placemark.street}, ${placemark.locality}, ${placemark.country}';
      }
    } catch (e) {
      debugPrint('Chyba při získávání adresy: $e');
    }
    return 'Neznámá adresa';
  }

  /// Získá koordináty z adresy
  Future<LatLng?> getCoordinatesFromAddress(String address) async {
    try {
      final locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        final location = locations.first;
        return LatLng(location.latitude, location.longitude);
      }
    } catch (e) {
      debugPrint('Chyba při získávání koordinátů: $e');
    }
    return null;
  }

  /// Nastaví kontroler mapy
  void setMapController(GoogleMapController controller) {
    if (_mapController?.isCompleted == false) {
      _mapController?.complete(controller);
    } else {
      _mapController = Completer<GoogleMapController>();
      _mapController?.complete(controller);
    }
  }

  /// Přesune mapu na danou pozici
  Future<void> moveToPosition(LatLng position, {double zoom = 15.0}) async {
    final controller = await _mapController?.future;
    await controller?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(target: position, zoom: zoom),
      ),
    );
  }

  /// Přesune mapu na aktuální polohu uživatele
  Future<void> moveToCurrentLocation() async {
    if (_currentPosition != null) {
      final position = LatLng(
        _currentPosition!.latitude,
        _currentPosition!.longitude,
      );
      await moveToPosition(position);
    }
  }

  /// Přesune mapu na Chorvatsko
  Future<void> moveToCroatia() async {
    await moveToPosition(_croatiaCenter, zoom: _defaultZoom);
  }

  /// Nastaví stav načítání
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Nastaví chybu
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// Vymaže chybu
  void clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _mapController = null;
    super.dispose();
  }
}
