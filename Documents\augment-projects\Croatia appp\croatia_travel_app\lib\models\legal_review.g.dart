// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'legal_review.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LegalReviewRequest _$LegalReviewRequestFromJson(Map<String, dynamic> json) =>
    LegalReviewRequest(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: $enumDecode(_$LegalReviewTypeEnumMap, json['type']),
      requiredExpertise: (json['requiredExpertise'] as List<dynamic>)
          .map((e) => $enumDecode(_$LegalExpertiseEnumMap, e))
          .toList(),
      priority: $enumDecode(_$PriorityEnumMap, json['priority']),
      deadline: DateTime.parse(json['deadline'] as String),
      documents:
          (json['documents'] as List<dynamic>).map((e) => e as String).toList(),
      metadata: json['metadata'] as Map<String, dynamic>,
      createdAt: DateTime.parse(json['createdAt'] as String),
      requestedBy: json['requestedBy'] as String,
      status: $enumDecode(_$ReviewStatusEnumMap, json['status']),
      assignedExpertId: json['assignedExpertId'] as String?,
    );

Map<String, dynamic> _$LegalReviewRequestToJson(LegalReviewRequest instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': _$LegalReviewTypeEnumMap[instance.type]!,
      'requiredExpertise': instance.requiredExpertise
          .map((e) => _$LegalExpertiseEnumMap[e]!)
          .toList(),
      'priority': _$PriorityEnumMap[instance.priority]!,
      'deadline': instance.deadline.toIso8601String(),
      'documents': instance.documents,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
      'requestedBy': instance.requestedBy,
      'status': _$ReviewStatusEnumMap[instance.status]!,
      'assignedExpertId': instance.assignedExpertId,
    };

const _$LegalReviewTypeEnumMap = {
  LegalReviewType.compliance: 'compliance',
  LegalReviewType.contract: 'contract',
  LegalReviewType.privacy: 'privacy',
  LegalReviewType.terms: 'terms',
  LegalReviewType.policy: 'policy',
  LegalReviewType.regulation: 'regulation',
};

const _$LegalExpertiseEnumMap = {
  LegalExpertise.gdpr: 'gdpr',
  LegalExpertise.contracts: 'contracts',
  LegalExpertise.intellectual: 'intellectual',
  LegalExpertise.employment: 'employment',
  LegalExpertise.corporate: 'corporate',
  LegalExpertise.litigation: 'litigation',
};

const _$PriorityEnumMap = {
  Priority.low: 'low',
  Priority.medium: 'medium',
  Priority.high: 'high',
  Priority.urgent: 'urgent',
};

const _$ReviewStatusEnumMap = {
  ReviewStatus.pending: 'pending',
  ReviewStatus.inProgress: 'inProgress',
  ReviewStatus.completed: 'completed',
  ReviewStatus.rejected: 'rejected',
  ReviewStatus.onHold: 'onHold',
};

LegalExpert _$LegalExpertFromJson(Map<String, dynamic> json) => LegalExpert(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      expertise: (json['expertise'] as List<dynamic>)
          .map((e) => $enumDecode(_$LegalExpertiseEnumMap, e))
          .toList(),
      experienceYears: (json['experienceYears'] as num).toInt(),
      rating: (json['rating'] as num).toDouble(),
      isAvailable: json['isAvailable'] as bool,
      currentWorkload: (json['currentWorkload'] as num).toInt(),
      certifications: (json['certifications'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      bio: json['bio'] as String?,
    );

Map<String, dynamic> _$LegalExpertToJson(LegalExpert instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'expertise':
          instance.expertise.map((e) => _$LegalExpertiseEnumMap[e]!).toList(),
      'experienceYears': instance.experienceYears,
      'rating': instance.rating,
      'isAvailable': instance.isAvailable,
      'currentWorkload': instance.currentWorkload,
      'certifications': instance.certifications,
      'bio': instance.bio,
    };

LegalFinding _$LegalFindingFromJson(Map<String, dynamic> json) => LegalFinding(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      severity: $enumDecode(_$FindingSeverityEnumMap, json['severity']),
      recommendation: json['recommendation'] as String,
      references: (json['references'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      requiresAction: json['requiresAction'] as bool,
    );

Map<String, dynamic> _$LegalFindingToJson(LegalFinding instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'severity': _$FindingSeverityEnumMap[instance.severity]!,
      'recommendation': instance.recommendation,
      'references': instance.references,
      'requiresAction': instance.requiresAction,
    };

const _$FindingSeverityEnumMap = {
  FindingSeverity.low: 'low',
  FindingSeverity.medium: 'medium',
  FindingSeverity.high: 'high',
  FindingSeverity.critical: 'critical',
};

LegalReviewReport _$LegalReviewReportFromJson(Map<String, dynamic> json) =>
    LegalReviewReport(
      id: json['id'] as String,
      requestId: json['requestId'] as String,
      expertId: json['expertId'] as String,
      title: json['title'] as String,
      summary: json['summary'] as String,
      overallStatus:
          $enumDecode(_$ComplianceStatusEnumMap, json['overallStatus']),
      findings: (json['findings'] as List<dynamic>)
          .map((e) => LegalFinding.fromJson(e as Map<String, dynamic>))
          .toList(),
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      completedAt: DateTime.parse(json['completedAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$LegalReviewReportToJson(LegalReviewReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'requestId': instance.requestId,
      'expertId': instance.expertId,
      'title': instance.title,
      'summary': instance.summary,
      'overallStatus': _$ComplianceStatusEnumMap[instance.overallStatus]!,
      'findings': instance.findings,
      'recommendations': instance.recommendations,
      'completedAt': instance.completedAt.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$ComplianceStatusEnumMap = {
  ComplianceStatus.compliant: 'compliant',
  ComplianceStatus.nonCompliant: 'nonCompliant',
  ComplianceStatus.partiallyCompliant: 'partiallyCompliant',
  ComplianceStatus.unknown: 'unknown',
};

LegalReviewEvent _$LegalReviewEventFromJson(Map<String, dynamic> json) =>
    LegalReviewEvent(
      id: json['id'] as String,
      type: $enumDecode(_$LegalEventTypeEnumMap, json['type']),
      requestId: json['requestId'] as String,
      expertId: json['expertId'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      description: json['description'] as String,
      data: json['data'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$LegalReviewEventToJson(LegalReviewEvent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$LegalEventTypeEnumMap[instance.type]!,
      'requestId': instance.requestId,
      'expertId': instance.expertId,
      'timestamp': instance.timestamp.toIso8601String(),
      'description': instance.description,
      'data': instance.data,
    };

const _$LegalEventTypeEnumMap = {
  LegalEventType.reviewRequested: 'reviewRequested',
  LegalEventType.expertAssigned: 'expertAssigned',
  LegalEventType.reviewStarted: 'reviewStarted',
  LegalEventType.reviewCompleted: 'reviewCompleted',
  LegalEventType.reportGenerated: 'reportGenerated',
};

LegalComplianceOverview _$LegalComplianceOverviewFromJson(
        Map<String, dynamic> json) =>
    LegalComplianceOverview(
      reportDate: DateTime.parse(json['reportDate'] as String),
      totalReviews: (json['totalReviews'] as num).toInt(),
      completedReviews: (json['completedReviews'] as num).toInt(),
      pendingReviews: (json['pendingReviews'] as num).toInt(),
      criticalFindings: (json['criticalFindings'] as num).toInt(),
      highFindings: (json['highFindings'] as num).toInt(),
      overallComplianceScore:
          (json['overallComplianceScore'] as num).toDouble(),
      topRisks:
          (json['topRisks'] as List<dynamic>).map((e) => e as String).toList(),
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$LegalComplianceOverviewToJson(
        LegalComplianceOverview instance) =>
    <String, dynamic>{
      'reportDate': instance.reportDate.toIso8601String(),
      'totalReviews': instance.totalReviews,
      'completedReviews': instance.completedReviews,
      'pendingReviews': instance.pendingReviews,
      'criticalFindings': instance.criticalFindings,
      'highFindings': instance.highFindings,
      'overallComplianceScore': instance.overallComplianceScore,
      'topRisks': instance.topRisks,
      'recommendations': instance.recommendations,
    };
