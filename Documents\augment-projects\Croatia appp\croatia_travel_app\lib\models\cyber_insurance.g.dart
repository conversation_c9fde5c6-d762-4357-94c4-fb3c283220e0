// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cyber_insurance.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RiskFactor _$RiskFactorFromJson(Map<String, dynamic> json) => RiskFactor(
      category: json['category'] as String,
      score: (json['score'] as num).toDouble(),
      factors:
          (json['factors'] as List<dynamic>).map((e) => e as String).toList(),
      impact: $enumDecode(_$RiskImpactEnumMap, json['impact']),
    );

Map<String, dynamic> _$RiskFactorToJson(RiskFactor instance) =>
    <String, dynamic>{
      'category': instance.category,
      'score': instance.score,
      'factors': instance.factors,
      'impact': _$RiskImpactEnumMap[instance.impact]!,
    };

const _$RiskImpactEnumMap = {
  RiskImpact.low: 'low',
  RiskImpact.medium: 'medium',
  RiskImpact.high: 'high',
  RiskImpact.critical: 'critical',
};

RiskAssessment _$RiskAssessmentFromJson(Map<String, dynamic> json) =>
    RiskAssessment(
      id: json['id'] as String,
      conductedAt: DateTime.parse(json['conductedAt'] as String),
      overallRiskScore: (json['overallRiskScore'] as num).toDouble(),
      riskLevel: $enumDecode(_$RiskLevelEnumMap, json['riskLevel']),
      dataRisk: RiskFactor.fromJson(json['dataRisk'] as Map<String, dynamic>),
      systemRisk:
          RiskFactor.fromJson(json['systemRisk'] as Map<String, dynamic>),
      complianceRisk:
          RiskFactor.fromJson(json['complianceRisk'] as Map<String, dynamic>),
      businessRisk:
          RiskFactor.fromJson(json['businessRisk'] as Map<String, dynamic>),
      thirdPartyRisk:
          RiskFactor.fromJson(json['thirdPartyRisk'] as Map<String, dynamic>),
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      estimatedAnnualLoss: (json['estimatedAnnualLoss'] as num).toDouble(),
      nextAssessmentDue: DateTime.parse(json['nextAssessmentDue'] as String),
    );

Map<String, dynamic> _$RiskAssessmentToJson(RiskAssessment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'conductedAt': instance.conductedAt.toIso8601String(),
      'overallRiskScore': instance.overallRiskScore,
      'riskLevel': _$RiskLevelEnumMap[instance.riskLevel]!,
      'dataRisk': instance.dataRisk,
      'systemRisk': instance.systemRisk,
      'complianceRisk': instance.complianceRisk,
      'businessRisk': instance.businessRisk,
      'thirdPartyRisk': instance.thirdPartyRisk,
      'recommendations': instance.recommendations,
      'estimatedAnnualLoss': instance.estimatedAnnualLoss,
      'nextAssessmentDue': instance.nextAssessmentDue.toIso8601String(),
    };

const _$RiskLevelEnumMap = {
  RiskLevel.low: 'low',
  RiskLevel.medium: 'medium',
  RiskLevel.high: 'high',
  RiskLevel.critical: 'critical',
  RiskLevel.unknown: 'unknown',
};

InsuranceQuote _$InsuranceQuoteFromJson(Map<String, dynamic> json) =>
    InsuranceQuote(
      id: json['id'] as String,
      riskAssessmentId: json['riskAssessmentId'] as String,
      coverageType: $enumDecode(_$CoverageTypeEnumMap, json['coverageType']),
      coverageLimit: (json['coverageLimit'] as num).toDouble(),
      annualPremium: (json['annualPremium'] as num).toDouble(),
      deductible: (json['deductible'] as num).toDouble(),
      policyTerm: Duration(microseconds: (json['policyTerm'] as num).toInt()),
      validUntil: DateTime.parse(json['validUntil'] as String),
      coverageDetails: json['coverageDetails'] as Map<String, dynamic>,
      exclusions: (json['exclusions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      riskFactors: (json['riskFactors'] as List<dynamic>)
          .map((e) => RiskFactor.fromJson(e as Map<String, dynamic>))
          .toList(),
      discounts: (json['discounts'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$InsuranceQuoteToJson(InsuranceQuote instance) =>
    <String, dynamic>{
      'id': instance.id,
      'riskAssessmentId': instance.riskAssessmentId,
      'coverageType': _$CoverageTypeEnumMap[instance.coverageType]!,
      'coverageLimit': instance.coverageLimit,
      'annualPremium': instance.annualPremium,
      'deductible': instance.deductible,
      'policyTerm': instance.policyTerm.inMicroseconds,
      'validUntil': instance.validUntil.toIso8601String(),
      'coverageDetails': instance.coverageDetails,
      'exclusions': instance.exclusions,
      'riskFactors': instance.riskFactors,
      'discounts': instance.discounts,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$CoverageTypeEnumMap = {
  CoverageType.basic: 'basic',
  CoverageType.standard: 'standard',
  CoverageType.comprehensive: 'comprehensive',
  CoverageType.enterprise: 'enterprise',
};

InsurancePolicy _$InsurancePolicyFromJson(Map<String, dynamic> json) =>
    InsurancePolicy(
      id: json['id'] as String,
      quoteId: json['quoteId'] as String,
      policyNumber: json['policyNumber'] as String,
      insurer: json['insurer'] as String,
      coverageType: $enumDecode(_$CoverageTypeEnumMap, json['coverageType']),
      coverageLimit: (json['coverageLimit'] as num).toDouble(),
      annualPremium: (json['annualPremium'] as num).toDouble(),
      deductible: (json['deductible'] as num).toDouble(),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      status: $enumDecode(_$PolicyStatusEnumMap, json['status']),
      companyDetails: json['companyDetails'] as Map<String, dynamic>,
      coverageDetails: json['coverageDetails'] as Map<String, dynamic>,
      exclusions: (json['exclusions'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      claims:
          (json['claims'] as List<dynamic>).map((e) => e as String).toList(),
      lastPremiumPaid: DateTime.parse(json['lastPremiumPaid'] as String),
      nextPremiumDue: DateTime.parse(json['nextPremiumDue'] as String),
    );

Map<String, dynamic> _$InsurancePolicyToJson(InsurancePolicy instance) =>
    <String, dynamic>{
      'id': instance.id,
      'quoteId': instance.quoteId,
      'policyNumber': instance.policyNumber,
      'insurer': instance.insurer,
      'coverageType': _$CoverageTypeEnumMap[instance.coverageType]!,
      'coverageLimit': instance.coverageLimit,
      'annualPremium': instance.annualPremium,
      'deductible': instance.deductible,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'status': _$PolicyStatusEnumMap[instance.status]!,
      'companyDetails': instance.companyDetails,
      'coverageDetails': instance.coverageDetails,
      'exclusions': instance.exclusions,
      'claims': instance.claims,
      'lastPremiumPaid': instance.lastPremiumPaid.toIso8601String(),
      'nextPremiumDue': instance.nextPremiumDue.toIso8601String(),
    };

const _$PolicyStatusEnumMap = {
  PolicyStatus.active: 'active',
  PolicyStatus.inactive: 'inactive',
  PolicyStatus.expired: 'expired',
  PolicyStatus.cancelled: 'cancelled',
  PolicyStatus.suspended: 'suspended',
};

InsuranceClaim _$InsuranceClaimFromJson(Map<String, dynamic> json) =>
    InsuranceClaim(
      id: json['id'] as String,
      policyId: json['policyId'] as String,
      claimNumber: json['claimNumber'] as String,
      claimType: $enumDecode(_$ClaimTypeEnumMap, json['claimType']),
      incidentDescription: json['incidentDescription'] as String,
      incidentDate: DateTime.parse(json['incidentDate'] as String),
      estimatedLoss: (json['estimatedLoss'] as num).toDouble(),
      status: $enumDecode(_$ClaimStatusEnumMap, json['status']),
      filedAt: DateTime.parse(json['filedAt'] as String),
      supportingDocuments: (json['supportingDocuments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      additionalInfo: json['additionalInfo'] as Map<String, dynamic>,
      updates: (json['updates'] as List<dynamic>)
          .map((e) => ClaimUpdate.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$InsuranceClaimToJson(InsuranceClaim instance) =>
    <String, dynamic>{
      'id': instance.id,
      'policyId': instance.policyId,
      'claimNumber': instance.claimNumber,
      'claimType': _$ClaimTypeEnumMap[instance.claimType]!,
      'incidentDescription': instance.incidentDescription,
      'incidentDate': instance.incidentDate.toIso8601String(),
      'estimatedLoss': instance.estimatedLoss,
      'status': _$ClaimStatusEnumMap[instance.status]!,
      'filedAt': instance.filedAt.toIso8601String(),
      'supportingDocuments': instance.supportingDocuments,
      'additionalInfo': instance.additionalInfo,
      'updates': instance.updates,
    };

const _$ClaimTypeEnumMap = {
  ClaimType.dataBreach: 'dataBreach',
  ClaimType.cyberAttack: 'cyberAttack',
  ClaimType.systemFailure: 'systemFailure',
  ClaimType.businessInterruption: 'businessInterruption',
  ClaimType.thirdPartyLiability: 'thirdPartyLiability',
  ClaimType.regulatoryFines: 'regulatoryFines',
  ClaimType.forensicInvestigation: 'forensicInvestigation',
};

const _$ClaimStatusEnumMap = {
  ClaimStatus.submitted: 'submitted',
  ClaimStatus.underReview: 'underReview',
  ClaimStatus.approved: 'approved',
  ClaimStatus.denied: 'denied',
  ClaimStatus.settled: 'settled',
  ClaimStatus.closed: 'closed',
};

ClaimUpdate _$ClaimUpdateFromJson(Map<String, dynamic> json) => ClaimUpdate(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      message: json['message'] as String,
      newStatus: $enumDecode(_$ClaimStatusEnumMap, json['newStatus']),
      updatedBy: json['updatedBy'] as String,
    );

Map<String, dynamic> _$ClaimUpdateToJson(ClaimUpdate instance) =>
    <String, dynamic>{
      'id': instance.id,
      'timestamp': instance.timestamp.toIso8601String(),
      'message': instance.message,
      'newStatus': _$ClaimStatusEnumMap[instance.newStatus]!,
      'updatedBy': instance.updatedBy,
    };

InsuranceEvent _$InsuranceEventFromJson(Map<String, dynamic> json) =>
    InsuranceEvent(
      type: $enumDecode(_$InsuranceEventTypeEnumMap, json['type']),
      policyId: json['policyId'] as String?,
      claimId: json['claimId'] as String?,
      quoteId: json['quoteId'] as String?,
      assessmentId: json['assessmentId'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      data: json['data'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$InsuranceEventToJson(InsuranceEvent instance) =>
    <String, dynamic>{
      'type': _$InsuranceEventTypeEnumMap[instance.type]!,
      'policyId': instance.policyId,
      'claimId': instance.claimId,
      'quoteId': instance.quoteId,
      'assessmentId': instance.assessmentId,
      'timestamp': instance.timestamp.toIso8601String(),
      'data': instance.data,
    };

const _$InsuranceEventTypeEnumMap = {
  InsuranceEventType.riskAssessmentCompleted: 'riskAssessmentCompleted',
  InsuranceEventType.quoteGenerated: 'quoteGenerated',
  InsuranceEventType.policyPurchased: 'policyPurchased',
  InsuranceEventType.claimFiled: 'claimFiled',
  InsuranceEventType.claimProcessed: 'claimProcessed',
  InsuranceEventType.policyRenewed: 'policyRenewed',
  InsuranceEventType.coverageUpdated: 'coverageUpdated',
};

CoverageStatus _$CoverageStatusFromJson(Map<String, dynamic> json) =>
    CoverageStatus(
      policyId: json['policyId'] as String,
      isActive: json['isActive'] as bool,
      lastChecked: DateTime.parse(json['lastChecked'] as String),
      alerts:
          (json['alerts'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$CoverageStatusToJson(CoverageStatus instance) =>
    <String, dynamic>{
      'policyId': instance.policyId,
      'isActive': instance.isActive,
      'lastChecked': instance.lastChecked.toIso8601String(),
      'alerts': instance.alerts,
    };

CoverageReport _$CoverageReportFromJson(Map<String, dynamic> json) =>
    CoverageReport(
      reportDate: DateTime.parse(json['reportDate'] as String),
      activePolicies: (json['activePolicies'] as num).toInt(),
      totalCoverageLimit: (json['totalCoverageLimit'] as num).toDouble(),
      totalAnnualPremiums: (json['totalAnnualPremiums'] as num).toDouble(),
      openClaims: (json['openClaims'] as num).toInt(),
      currentRiskLevel:
          $enumDecode(_$RiskLevelEnumMap, json['currentRiskLevel']),
      coverageGaps: (json['coverageGaps'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      nextRenewalDate: json['nextRenewalDate'] == null
          ? null
          : DateTime.parse(json['nextRenewalDate'] as String),
      complianceStatus: json['complianceStatus'] as bool,
    );

Map<String, dynamic> _$CoverageReportToJson(CoverageReport instance) =>
    <String, dynamic>{
      'reportDate': instance.reportDate.toIso8601String(),
      'activePolicies': instance.activePolicies,
      'totalCoverageLimit': instance.totalCoverageLimit,
      'totalAnnualPremiums': instance.totalAnnualPremiums,
      'openClaims': instance.openClaims,
      'currentRiskLevel': _$RiskLevelEnumMap[instance.currentRiskLevel]!,
      'coverageGaps': instance.coverageGaps,
      'recommendations': instance.recommendations,
      'nextRenewalDate': instance.nextRenewalDate?.toIso8601String(),
      'complianceStatus': instance.complianceStatus,
    };
