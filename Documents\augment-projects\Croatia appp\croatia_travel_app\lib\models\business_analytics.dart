import 'package:json_annotation/json_annotation.dart';

part 'business_analytics.g.dart';

/// Business analytics framework enum
enum AnalyticsFramework {
  revenue,
  user,
  engagement,
  conversion,
  retention,
  custom,
}

/// Report type enum
enum ReportType { daily, weekly, monthly, quarterly, yearly, custom }

/// Export format enum
enum ExportFormat { pdf, excel, csv, json }

/// Metric type enum
enum MetricType { revenue, users, sessions, conversion, retention, engagement }

/// Analytics dashboard model
@JsonSerializable()
class AnalyticsDashboard {
  final DateTime lastUpdated;
  final double totalRevenue;
  final int totalUsers;
  final int activeUsers;
  final double conversionRate;
  final double retentionRate;
  final Map<String, double> revenueByCategory;
  final Map<String, int> usersByRegion;
  final List<AnalyticsEvent> recentEvents;
  final Map<String, dynamic> trends;

  const AnalyticsDashboard({
    required this.lastUpdated,
    required this.totalRevenue,
    required this.totalUsers,
    required this.activeUsers,
    required this.conversionRate,
    required this.retentionRate,
    required this.revenueByCategory,
    required this.usersByRegion,
    required this.recentEvents,
    required this.trends,
  });

  factory AnalyticsDashboard.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsDashboardFromJson(json);
  Map<String, dynamic> toJson() => _$AnalyticsDashboardToJson(this);
}

/// Analytics event model
@JsonSerializable()
class AnalyticsEvent {
  final String id;
  final String name;
  final String category;
  final Map<String, dynamic> properties;
  final DateTime timestamp;
  final String? userId;
  final String? sessionId;

  const AnalyticsEvent({
    required this.id,
    required this.name,
    required this.category,
    required this.properties,
    required this.timestamp,
    this.userId,
    this.sessionId,
  });

  factory AnalyticsEvent.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsEventFromJson(json);
  Map<String, dynamic> toJson() => _$AnalyticsEventToJson(this);
}

/// Analytics report model
@JsonSerializable()
class AnalyticsReport {
  final String id;
  final ReportType type;
  final DateTime fromDate;
  final DateTime toDate;
  final List<MetricType> metrics;
  final AnalyticsDashboard dashboard;
  final List<String> insights;
  final List<String> recommendations;
  final DateTime generatedAt;
  final ExportFormat? exportFormat;

  const AnalyticsReport({
    required this.id,
    required this.type,
    required this.fromDate,
    required this.toDate,
    required this.metrics,
    required this.dashboard,
    required this.insights,
    required this.recommendations,
    required this.generatedAt,
    this.exportFormat,
  });

  factory AnalyticsReport.fromJson(Map<String, dynamic> json) =>
      _$AnalyticsReportFromJson(json);
  Map<String, dynamic> toJson() => _$AnalyticsReportToJson(this);
}

/// User analytics model
@JsonSerializable()
class UserAnalytics {
  final String userId;
  final int totalSessions;
  final Duration totalTime;
  final DateTime firstVisit;
  final DateTime lastVisit;
  final List<String> visitedPages;
  final Map<String, int> actionCounts;
  final double engagementScore;

  const UserAnalytics({
    required this.userId,
    required this.totalSessions,
    required this.totalTime,
    required this.firstVisit,
    required this.lastVisit,
    required this.visitedPages,
    required this.actionCounts,
    required this.engagementScore,
  });

  factory UserAnalytics.fromJson(Map<String, dynamic> json) =>
      _$UserAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$UserAnalyticsToJson(this);
}

/// Revenue analytics model
@JsonSerializable()
class RevenueAnalytics {
  final double totalRevenue;
  final double monthlyRecurringRevenue;
  final double averageOrderValue;
  final int totalOrders;
  final Map<String, double> revenueByProduct;
  final Map<String, double> revenueByRegion;
  final List<RevenueMetric> monthlyMetrics;

  const RevenueAnalytics({
    required this.totalRevenue,
    required this.monthlyRecurringRevenue,
    required this.averageOrderValue,
    required this.totalOrders,
    required this.revenueByProduct,
    required this.revenueByRegion,
    required this.monthlyMetrics,
  });

  factory RevenueAnalytics.fromJson(Map<String, dynamic> json) =>
      _$RevenueAnalyticsFromJson(json);
  Map<String, dynamic> toJson() => _$RevenueAnalyticsToJson(this);
}

/// Revenue metric model
@JsonSerializable()
class RevenueMetric {
  final DateTime month;
  final double revenue;
  final int orders;
  final double averageOrderValue;
  final double growthRate;

  const RevenueMetric({
    required this.month,
    required this.revenue,
    required this.orders,
    required this.averageOrderValue,
    required this.growthRate,
  });

  factory RevenueMetric.fromJson(Map<String, dynamic> json) =>
      _$RevenueMetricFromJson(json);
  Map<String, dynamic> toJson() => _$RevenueMetricToJson(this);
}

/// Conversion funnel model
@JsonSerializable()
class ConversionFunnel {
  final String name;
  final List<FunnelStep> steps;
  final double overallConversionRate;
  final DateTime analyzedAt;

  const ConversionFunnel({
    required this.name,
    required this.steps,
    required this.overallConversionRate,
    required this.analyzedAt,
  });

  factory ConversionFunnel.fromJson(Map<String, dynamic> json) =>
      _$ConversionFunnelFromJson(json);
  Map<String, dynamic> toJson() => _$ConversionFunnelToJson(this);
}

/// Funnel step model
@JsonSerializable()
class FunnelStep {
  final String name;
  final int users;
  final double conversionRate;
  final double dropOffRate;

  const FunnelStep({
    required this.name,
    required this.users,
    required this.conversionRate,
    required this.dropOffRate,
  });

  factory FunnelStep.fromJson(Map<String, dynamic> json) =>
      _$FunnelStepFromJson(json);
  Map<String, dynamic> toJson() => _$FunnelStepToJson(this);
}

/// Cohort analysis model
@JsonSerializable()
class CohortAnalysis {
  final String cohortType;
  final DateTime startDate;
  final DateTime endDate;
  final List<CohortData> cohorts;
  final Map<String, double> retentionRates;

  const CohortAnalysis({
    required this.cohortType,
    required this.startDate,
    required this.endDate,
    required this.cohorts,
    required this.retentionRates,
  });

  factory CohortAnalysis.fromJson(Map<String, dynamic> json) =>
      _$CohortAnalysisFromJson(json);
  Map<String, dynamic> toJson() => _$CohortAnalysisToJson(this);
}

/// Cohort data model
@JsonSerializable()
class CohortData {
  final DateTime cohortDate;
  final int initialUsers;
  final Map<int, int> retentionByPeriod;
  final Map<int, double> retentionRates;

  const CohortData({
    required this.cohortDate,
    required this.initialUsers,
    required this.retentionByPeriod,
    required this.retentionRates,
  });

  factory CohortData.fromJson(Map<String, dynamic> json) =>
      _$CohortDataFromJson(json);
  Map<String, dynamic> toJson() => _$CohortDataToJson(this);
}

/// Business Client model
@JsonSerializable()
class BusinessClient {
  final String id;
  final String companyName;
  final String industry;
  final String contactEmail;
  final String contactPhone;
  final DateTime onboardedAt;
  final bool isActive;
  final double monthlyRevenue;
  final String tier;

  const BusinessClient({
    required this.id,
    required this.companyName,
    required this.industry,
    required this.contactEmail,
    required this.contactPhone,
    required this.onboardedAt,
    required this.isActive,
    required this.monthlyRevenue,
    required this.tier,
  });

  factory BusinessClient.fromJson(Map<String, dynamic> json) =>
      _$BusinessClientFromJson(json);
  Map<String, dynamic> toJson() => _$BusinessClientToJson(this);
}

/// Revenue Record model
@JsonSerializable()
class RevenueRecord {
  final String id;
  final String clientId;
  final String userId;
  final double amount;
  final String source;
  final RevenueType type;
  final DateTime date;
  final String currency;

  const RevenueRecord({
    required this.id,
    required this.clientId,
    required this.userId,
    required this.amount,
    required this.source,
    required this.type,
    required this.date,
    required this.currency,
  });

  factory RevenueRecord.fromJson(Map<String, dynamic> json) =>
      _$RevenueRecordFromJson(json);
  Map<String, dynamic> toJson() => _$RevenueRecordToJson(this);
}

/// Revenue Type enum
enum RevenueType { subscription, oneTime, commission, advertising }

/// User Engagement Metric model
@JsonSerializable()
class UserEngagementMetric {
  final String id;
  final String userId;
  final String eventType;
  final DateTime timestamp;
  final Duration sessionDuration;
  final int pageViews;
  final Map<String, dynamic> properties;

  const UserEngagementMetric({
    required this.id,
    required this.userId,
    required this.eventType,
    required this.timestamp,
    required this.sessionDuration,
    required this.pageViews,
    required this.properties,
  });

  factory UserEngagementMetric.fromJson(Map<String, dynamic> json) =>
      _$UserEngagementMetricFromJson(json);
  Map<String, dynamic> toJson() => _$UserEngagementMetricToJson(this);
}

/// Business Report model
@JsonSerializable()
class BusinessReport {
  final String id;
  final String name;
  final ReportType type;
  final DateTime fromDate;
  final DateTime toDate;
  final Map<String, dynamic> data;
  final List<String> metrics;
  final List<String> dimensions;
  final DateTime generatedAt;
  final String generatedBy;

  const BusinessReport({
    required this.id,
    required this.name,
    required this.type,
    required this.fromDate,
    required this.toDate,
    required this.data,
    required this.metrics,
    required this.dimensions,
    required this.generatedAt,
    required this.generatedBy,
  });

  factory BusinessReport.fromJson(Map<String, dynamic> json) =>
      _$BusinessReportFromJson(json);
  Map<String, dynamic> toJson() => _$BusinessReportToJson(this);

  factory BusinessReport.empty() => BusinessReport(
    id: 'empty',
    name: 'Empty Report',
    type: ReportType.custom,
    fromDate: DateTime.now(),
    toDate: DateTime.now(),
    data: {},
    metrics: [],
    dimensions: [],
    generatedAt: DateTime.now(),
    generatedBy: 'system',
  );
}

/// Business Dashboard model
@JsonSerializable()
class BusinessDashboard {
  final DateTime periodStart;
  final DateTime periodEnd;
  final String? clientId;
  final RevenueMetrics revenueMetrics;
  final UserMetrics userMetrics;
  final EngagementMetrics engagementMetrics;
  final RetentionMetrics retentionMetrics;
  final ConversionMetrics conversionMetrics;
  final PerformanceMetrics performanceMetrics;
  final CompetitorAnalysis competitorAnalysis;
  final BusinessForecasting forecasting;
  final DateTime generatedAt;

  const BusinessDashboard({
    required this.periodStart,
    required this.periodEnd,
    this.clientId,
    required this.revenueMetrics,
    required this.userMetrics,
    required this.engagementMetrics,
    required this.retentionMetrics,
    required this.conversionMetrics,
    required this.performanceMetrics,
    required this.competitorAnalysis,
    required this.forecasting,
    required this.generatedAt,
  });

  factory BusinessDashboard.fromJson(Map<String, dynamic> json) =>
      _$BusinessDashboardFromJson(json);
  Map<String, dynamic> toJson() => _$BusinessDashboardToJson(this);

  factory BusinessDashboard.empty() => BusinessDashboard(
    periodStart: DateTime.now(),
    periodEnd: DateTime.now(),
    revenueMetrics: RevenueMetrics.empty(),
    userMetrics: UserMetrics.empty(),
    engagementMetrics: EngagementMetrics.empty(),
    retentionMetrics: RetentionMetrics.empty(),
    conversionMetrics: ConversionMetrics.empty(),
    performanceMetrics: PerformanceMetrics.empty(),
    competitorAnalysis: CompetitorAnalysis.empty(),
    forecasting: BusinessForecasting.empty(),
    generatedAt: DateTime.now(),
  );
}

/// Revenue Metrics model
@JsonSerializable()
class RevenueMetrics {
  final double totalRevenue;
  final double subscriptionRevenue;
  final double oneTimeRevenue;
  final double growthRate;
  final double arr;
  final double arpu;
  final Map<String, double> revenueBySource;
  final List<MonthlyRevenue> monthlyTrend;

  const RevenueMetrics({
    required this.totalRevenue,
    required this.subscriptionRevenue,
    required this.oneTimeRevenue,
    required this.growthRate,
    required this.arr,
    required this.arpu,
    required this.revenueBySource,
    required this.monthlyTrend,
  });

  factory RevenueMetrics.fromJson(Map<String, dynamic> json) =>
      _$RevenueMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$RevenueMetricsToJson(this);

  factory RevenueMetrics.empty() => RevenueMetrics(
    totalRevenue: 0,
    subscriptionRevenue: 0,
    oneTimeRevenue: 0,
    growthRate: 0,
    arr: 0,
    arpu: 0,
    revenueBySource: {},
    monthlyTrend: [],
  );
}

/// Monthly Revenue model
@JsonSerializable()
class MonthlyRevenue {
  final int year;
  final int month;
  final double revenue;

  const MonthlyRevenue({
    required this.year,
    required this.month,
    required this.revenue,
  });

  factory MonthlyRevenue.fromJson(Map<String, dynamic> json) =>
      _$MonthlyRevenueFromJson(json);
  Map<String, dynamic> toJson() => _$MonthlyRevenueToJson(this);
}

/// User Metrics model
@JsonSerializable()
class UserMetrics {
  final int totalUsers;
  final int activeUsers;
  final int newUsers;
  final int premiumUsers;
  final double userGrowthRate;
  final Map<String, int> usersByCountry;
  final Map<String, int> usersByPlatform;
  final Map<String, double> userAcquisitionChannels;

  const UserMetrics({
    required this.totalUsers,
    required this.activeUsers,
    required this.newUsers,
    required this.premiumUsers,
    required this.userGrowthRate,
    required this.usersByCountry,
    required this.usersByPlatform,
    required this.userAcquisitionChannels,
  });

  factory UserMetrics.fromJson(Map<String, dynamic> json) =>
      _$UserMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$UserMetricsToJson(this);

  factory UserMetrics.empty() => UserMetrics(
    totalUsers: 0,
    activeUsers: 0,
    newUsers: 0,
    premiumUsers: 0,
    userGrowthRate: 0,
    usersByCountry: {},
    usersByPlatform: {},
    userAcquisitionChannels: {},
  );
}

/// Engagement Metrics model
@JsonSerializable()
class EngagementMetrics {
  final int dailyActiveUsers;
  final int weeklyActiveUsers;
  final int monthlyActiveUsers;
  final Duration averageSessionDuration;
  final double sessionsPerUser;
  final double bounceRate;
  final int pageViews;
  final Map<String, double> featureUsage;
  final List<UserJourneyStep> userJourney;

  const EngagementMetrics({
    required this.dailyActiveUsers,
    required this.weeklyActiveUsers,
    required this.monthlyActiveUsers,
    required this.averageSessionDuration,
    required this.sessionsPerUser,
    required this.bounceRate,
    required this.pageViews,
    required this.featureUsage,
    required this.userJourney,
  });

  factory EngagementMetrics.fromJson(Map<String, dynamic> json) =>
      _$EngagementMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$EngagementMetricsToJson(this);

  factory EngagementMetrics.empty() => EngagementMetrics(
    dailyActiveUsers: 0,
    weeklyActiveUsers: 0,
    monthlyActiveUsers: 0,
    averageSessionDuration: Duration.zero,
    sessionsPerUser: 0,
    bounceRate: 0,
    pageViews: 0,
    featureUsage: {},
    userJourney: [],
  );
}

/// User Journey Step model
@JsonSerializable()
class UserJourneyStep {
  final String name;
  final double conversionRate;
  final Duration averageTime;

  const UserJourneyStep(this.name, this.conversionRate, this.averageTime);

  factory UserJourneyStep.fromJson(Map<String, dynamic> json) =>
      _$UserJourneyStepFromJson(json);
  Map<String, dynamic> toJson() => _$UserJourneyStepToJson(this);
}

/// Retention Metrics model
@JsonSerializable()
class RetentionMetrics {
  final double day1Retention;
  final double day7Retention;
  final double day30Retention;
  final Map<String, List<double>> cohortAnalysis;
  final double churnRate;
  final double ltv;
  final Map<String, double> retentionBySegment;

  const RetentionMetrics({
    required this.day1Retention,
    required this.day7Retention,
    required this.day30Retention,
    required this.cohortAnalysis,
    required this.churnRate,
    required this.ltv,
    required this.retentionBySegment,
  });

  factory RetentionMetrics.fromJson(Map<String, dynamic> json) =>
      _$RetentionMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$RetentionMetricsToJson(this);

  factory RetentionMetrics.empty() => RetentionMetrics(
    day1Retention: 0,
    day7Retention: 0,
    day30Retention: 0,
    cohortAnalysis: {},
    churnRate: 0,
    ltv: 0,
    retentionBySegment: {},
  );
}

/// Conversion Metrics model
@JsonSerializable()
class ConversionMetrics {
  final double trialToSubscription;
  final double freeToTrial;
  final double visitorToSignup;
  final List<ConversionStep> conversionFunnel;
  final Map<String, double> conversionByChannel;
  final Duration timeToConversion;

  const ConversionMetrics({
    required this.trialToSubscription,
    required this.freeToTrial,
    required this.visitorToSignup,
    required this.conversionFunnel,
    required this.conversionByChannel,
    required this.timeToConversion,
  });

  factory ConversionMetrics.fromJson(Map<String, dynamic> json) =>
      _$ConversionMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$ConversionMetricsToJson(this);

  factory ConversionMetrics.empty() => ConversionMetrics(
    trialToSubscription: 0,
    freeToTrial: 0,
    visitorToSignup: 0,
    conversionFunnel: [],
    conversionByChannel: {},
    timeToConversion: Duration.zero,
  );
}

/// Conversion Step model
@JsonSerializable()
class ConversionStep {
  final String name;
  final int users;
  final double conversionRate;

  const ConversionStep(this.name, this.users, this.conversionRate);

  factory ConversionStep.fromJson(Map<String, dynamic> json) =>
      _$ConversionStepFromJson(json);
  Map<String, dynamic> toJson() => _$ConversionStepToJson(this);
}

/// Performance Metrics model
@JsonSerializable()
class PerformanceMetrics {
  final double averageLoadTime;
  final double uptime;
  final int errorRate;
  final Map<String, double> apiResponseTimes;
  final Map<String, int> errorsByType;
  final double crashRate;

  const PerformanceMetrics({
    required this.averageLoadTime,
    required this.uptime,
    required this.errorRate,
    required this.apiResponseTimes,
    required this.errorsByType,
    required this.crashRate,
  });

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) =>
      _$PerformanceMetricsFromJson(json);
  Map<String, dynamic> toJson() => _$PerformanceMetricsToJson(this);

  factory PerformanceMetrics.empty() => PerformanceMetrics(
    averageLoadTime: 0,
    uptime: 0,
    errorRate: 0,
    apiResponseTimes: {},
    errorsByType: {},
    crashRate: 0,
  );
}

/// Competitor Analysis model
@JsonSerializable()
class CompetitorAnalysis {
  final List<CompetitorData> competitors;
  final Map<String, double> marketShare;
  final Map<String, double> featureComparison;
  final Map<String, double> pricingComparison;

  const CompetitorAnalysis({
    required this.competitors,
    required this.marketShare,
    required this.featureComparison,
    required this.pricingComparison,
  });

  factory CompetitorAnalysis.fromJson(Map<String, dynamic> json) =>
      _$CompetitorAnalysisFromJson(json);
  Map<String, dynamic> toJson() => _$CompetitorAnalysisToJson(this);

  factory CompetitorAnalysis.empty() => CompetitorAnalysis(
    competitors: [],
    marketShare: {},
    featureComparison: {},
    pricingComparison: {},
  );
}

/// Competitor Data model
@JsonSerializable()
class CompetitorData {
  final String name;
  final double marketShare;
  final double rating;
  final List<String> strengths;
  final List<String> weaknesses;

  const CompetitorData({
    required this.name,
    required this.marketShare,
    required this.rating,
    required this.strengths,
    required this.weaknesses,
  });

  factory CompetitorData.fromJson(Map<String, dynamic> json) =>
      _$CompetitorDataFromJson(json);
  Map<String, dynamic> toJson() => _$CompetitorDataToJson(this);
}

/// Business Forecasting model
@JsonSerializable()
class BusinessForecasting {
  final List<RevenueForecast> revenueForecast;
  final List<UserGrowthForecast> userGrowthForecast;
  final Map<String, double> seasonalTrends;
  final double confidenceLevel;

  const BusinessForecasting({
    required this.revenueForecast,
    required this.userGrowthForecast,
    required this.seasonalTrends,
    required this.confidenceLevel,
  });

  factory BusinessForecasting.fromJson(Map<String, dynamic> json) =>
      _$BusinessForecastingFromJson(json);
  Map<String, dynamic> toJson() => _$BusinessForecastingToJson(this);

  factory BusinessForecasting.empty() => BusinessForecasting(
    revenueForecast: [],
    userGrowthForecast: [],
    seasonalTrends: {},
    confidenceLevel: 0,
  );
}

/// Revenue Forecast model
@JsonSerializable()
class RevenueForecast {
  final DateTime date;
  final double predictedRevenue;
  final double confidence;

  const RevenueForecast(this.date, this.predictedRevenue, this.confidence);

  factory RevenueForecast.fromJson(Map<String, dynamic> json) =>
      _$RevenueForecastFromJson(json);
  Map<String, dynamic> toJson() => _$RevenueForecastToJson(this);
}

/// User Growth Forecast model
@JsonSerializable()
class UserGrowthForecast {
  final DateTime date;
  final int predictedUsers;
  final double confidence;

  const UserGrowthForecast(this.date, this.predictedUsers, this.confidence);

  factory UserGrowthForecast.fromJson(Map<String, dynamic> json) =>
      _$UserGrowthForecastFromJson(json);
  Map<String, dynamic> toJson() => _$UserGrowthForecastToJson(this);
}

/// Market Trend model
@JsonSerializable()
class MarketTrend {
  final String name;
  final double growthRate;
  final TrendDirection direction;

  const MarketTrend(this.name, this.growthRate, this.direction);

  factory MarketTrend.fromJson(Map<String, dynamic> json) =>
      _$MarketTrendFromJson(json);
  Map<String, dynamic> toJson() => _$MarketTrendToJson(this);
}

/// Trend Direction enum
enum TrendDirection { up, down, stable }

/// Threat Level model
@JsonSerializable()
class ThreatLevel {
  final String name;
  final ThreatSeverity severity;
  final double probability;

  const ThreatLevel(this.name, this.severity, this.probability);

  factory ThreatLevel.fromJson(Map<String, dynamic> json) =>
      _$ThreatLevelFromJson(json);
  Map<String, dynamic> toJson() => _$ThreatLevelToJson(this);
}

/// Threat Severity enum
enum ThreatSeverity { low, medium, high, critical }

/// Revenue Projection model
@JsonSerializable()
class RevenueProjection {
  final int month;
  final double projectedRevenue;
  final double confidence;

  const RevenueProjection({
    required this.month,
    required this.projectedRevenue,
    required this.confidence,
  });

  factory RevenueProjection.fromJson(Map<String, dynamic> json) =>
      _$RevenueProjectionFromJson(json);
  Map<String, dynamic> toJson() => _$RevenueProjectionToJson(this);
}

/// User Growth Projection model
@JsonSerializable()
class UserGrowthProjection {
  final int month;
  final int projectedUsers;
  final double organicGrowth;
  final double paidGrowth;

  const UserGrowthProjection({
    required this.month,
    required this.projectedUsers,
    required this.organicGrowth,
    required this.paidGrowth,
  });

  factory UserGrowthProjection.fromJson(Map<String, dynamic> json) =>
      _$UserGrowthProjectionFromJson(json);
  Map<String, dynamic> toJson() => _$UserGrowthProjectionToJson(this);
}

/// Market Size Projection model
@JsonSerializable()
class MarketSizeProjection {
  final double currentMarketSize;
  final double projectedMarketSize;
  final double cagr;
  final double addressableMarket;

  const MarketSizeProjection({
    required this.currentMarketSize,
    required this.projectedMarketSize,
    required this.cagr,
    required this.addressableMarket,
  });

  factory MarketSizeProjection.fromJson(Map<String, dynamic> json) =>
      _$MarketSizeProjectionFromJson(json);
  Map<String, dynamic> toJson() => _$MarketSizeProjectionToJson(this);
}
