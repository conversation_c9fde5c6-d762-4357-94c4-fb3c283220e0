import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/multilingual_legal.dart';

/// 🌍 MULTILINGUAL LEGAL SERVICE - Multi-language legal documents
class MultilingualLegalService {
  static final MultilingualLegalService _instance =
      MultilingualLegalService._internal();
  factory MultilingualLegalService() => _instance;
  MultilingualLegalService._internal();

  bool _isInitialized = false;
  final Map<String, Map<String, LegalDocument>> _documents =
      {}; // language -> documentType -> document
  final List<SupportedLanguage> _supportedLanguages = [];
  final Map<String, TranslationStatus> _translationStatus = {};
  final StreamController<TranslationEvent> _eventController =
      StreamController.broadcast();

  /// Stream translation událostí
  Stream<TranslationEvent> get translationEvents => _eventController.stream;

  /// Inicializace služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🌍 Inicializuji Multilingual Legal Service...');

      await _loadSupportedLanguages();
      await _loadLegalDocuments();
      await _initializeTranslationTracking();

      _isInitialized = true;
      debugPrint('✅ Multilingual Legal Service inicializován');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci Multilingual Legal: $e');
      await _createDefaultLanguages();
      _isInitialized = true;
    }
  }

  /// Získání Privacy Policy v konkrétním jazyce
  Future<LegalDocument> getPrivacyPolicy(String languageCode) async {
    await _ensureDocumentExists('privacy_policy', languageCode);
    return _documents[languageCode]!['privacy_policy']!;
  }

  /// Získání Terms of Service v konkrétním jazyce
  Future<LegalDocument> getTermsOfService(String languageCode) async {
    await _ensureDocumentExists('terms_of_service', languageCode);
    return _documents[languageCode]!['terms_of_service']!;
  }

  /// Získání Cookie Policy v konkrétním jazyce
  Future<LegalDocument> getCookiePolicy(String languageCode) async {
    await _ensureDocumentExists('cookie_policy', languageCode);
    return _documents[languageCode]!['cookie_policy']!;
  }

  /// Získání Data Processing Agreement v konkrétním jazyce
  Future<LegalDocument> getDataProcessingAgreement(String languageCode) async {
    await _ensureDocumentExists('data_processing_agreement', languageCode);
    return _documents[languageCode]!['data_processing_agreement']!;
  }

  /// Získání všech dostupných jazyků
  List<SupportedLanguage> getSupportedLanguages() {
    return List.unmodifiable(_supportedLanguages);
  }

  /// Kontrola dostupnosti dokumentu v jazyce
  bool isDocumentAvailable(String documentType, String languageCode) {
    return _documents[languageCode]?.containsKey(documentType) ?? false;
  }

  /// Získání statusu překladu
  TranslationStatus getTranslationStatus(
    String documentType,
    String languageCode,
  ) {
    final key = '${documentType}_$languageCode';
    return _translationStatus[key] ?? TranslationStatus.notStarted;
  }

  /// Vytvoření nového překladu dokumentu
  Future<bool> createTranslation({
    required String documentType,
    required String sourceLanguage,
    required String targetLanguage,
    required String translatedContent,
    String? translatorId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final sourceDocument = _documents[sourceLanguage]?[documentType];
      if (sourceDocument == null) {
        throw Exception(
          'Source document not found: $documentType in $sourceLanguage',
        );
      }

      final translatedDocument = LegalDocument(
        id: '${documentType}_$targetLanguage',
        type: sourceDocument.type,
        title: await _translateTitle(sourceDocument.title, targetLanguage),
        content: translatedContent,
        language: targetLanguage,
        version: sourceDocument.version,
        effectiveDate: sourceDocument.effectiveDate,
        lastUpdated: DateTime.now(),
        translatedFrom: sourceLanguage,
        translatorId: translatorId,
        reviewStatus: ReviewStatus.pending,
        metadata: metadata ?? {},
      );

      // Uložení překladu
      _documents[targetLanguage] ??= {};
      _documents[targetLanguage]![documentType] = translatedDocument;

      // Aktualizace statusu
      final statusKey = '${documentType}_$targetLanguage';
      _translationStatus[statusKey] = TranslationStatus.translated;

      await _saveLegalDocuments();

      _eventController.add(
        TranslationEvent(
          type: TranslationEventType.translationCreated,
          documentType: documentType,
          sourceLanguage: sourceLanguage,
          targetLanguage: targetLanguage,
          timestamp: DateTime.now(),
          data: {'translatorId': translatorId},
        ),
      );

      debugPrint('✅ Překlad vytvořen: $documentType -> $targetLanguage');
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při vytváření překladu: $e');
      return false;
    }
  }

  /// Automatický překlad pomocí AI (simulace)
  Future<bool> generateAutoTranslation({
    required String documentType,
    required String sourceLanguage,
    required String targetLanguage,
  }) async {
    try {
      debugPrint(
        '🤖 Generuji automatický překlad: $documentType -> $targetLanguage',
      );

      final sourceDocument = _documents[sourceLanguage]?[documentType];
      if (sourceDocument == null) {
        throw Exception('Source document not found');
      }

      // Simulace AI překladu
      await Future.delayed(const Duration(seconds: 3));

      final translatedContent = await _simulateAITranslation(
        sourceDocument.content,
        sourceLanguage,
        targetLanguage,
      );

      final success = await createTranslation(
        documentType: documentType,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        translatedContent: translatedContent,
        translatorId: 'AI_TRANSLATOR',
        metadata: {
          'translationMethod': 'AI',
          'confidence': 0.85,
          'needsReview': true,
        },
      );

      if (success) {
        // Označit jako potřebuje review
        final statusKey = '${documentType}_$targetLanguage';
        _translationStatus[statusKey] = TranslationStatus.needsReview;
      }

      return success;
    } catch (e) {
      debugPrint('❌ Chyba při AI překladu: $e');
      return false;
    }
  }

  /// Review a schválení překladu
  Future<bool> reviewTranslation({
    required String documentType,
    required String languageCode,
    required bool approved,
    String? reviewerId,
    String? reviewNotes,
    List<String>? corrections,
  }) async {
    try {
      final document = _documents[languageCode]?[documentType];
      if (document == null) {
        throw Exception('Document not found for review');
      }

      final updatedDocument = document.copyWith(
        reviewStatus: approved ? ReviewStatus.approved : ReviewStatus.rejected,
        reviewerId: reviewerId,
        reviewNotes: reviewNotes,
        reviewedAt: DateTime.now(),
        corrections: corrections,
      );

      _documents[languageCode]![documentType] = updatedDocument;

      final statusKey = '${documentType}_$languageCode';
      _translationStatus[statusKey] = approved
          ? TranslationStatus.approved
          : TranslationStatus.rejected;

      await _saveLegalDocuments();

      _eventController.add(
        TranslationEvent(
          type: approved
              ? TranslationEventType.translationApproved
              : TranslationEventType.translationRejected,
          documentType: documentType,
          targetLanguage: languageCode,
          timestamp: DateTime.now(),
          data: {'reviewerId': reviewerId, 'reviewNotes': reviewNotes},
        ),
      );

      debugPrint(
        '✅ Překlad ${approved ? "schválen" : "zamítnut"}: $documentType ($languageCode)',
      );
      return true;
    } catch (e) {
      debugPrint('❌ Chyba při review překladu: $e');
      return false;
    }
  }

  /// Získání translation progress reportu
  Future<TranslationProgressReport> getTranslationProgress() async {
    final totalDocuments = _getAllDocumentTypes().length;
    final totalLanguages = _supportedLanguages.length;
    final totalTranslations = totalDocuments * totalLanguages;

    var completedTranslations = 0;
    var pendingTranslations = 0;
    var needsReviewTranslations = 0;

    for (final documentType in _getAllDocumentTypes()) {
      for (final language in _supportedLanguages) {
        final status = getTranslationStatus(documentType, language.code);
        switch (status) {
          case TranslationStatus.approved:
            completedTranslations++;
            break;
          case TranslationStatus.translated:
          case TranslationStatus.needsReview:
            needsReviewTranslations++;
            break;
          case TranslationStatus.inProgress:
            pendingTranslations++;
            break;
          case TranslationStatus.notStarted:
          case TranslationStatus.rejected:
            // Not counted as completed
            break;
        }
      }
    }

    final completionRate = (completedTranslations / totalTranslations * 100);

    final estimatedCompletion = await _estimateCompletionDate();

    return TranslationProgressReport(
      totalTranslations: totalTranslations,
      completedTranslations: completedTranslations,
      pendingTranslations: pendingTranslations,
      needsReviewTranslations: needsReviewTranslations,
      completionRate: completionRate,
      languageProgress: _calculateLanguageProgress(),
      documentProgress: _calculateDocumentProgress(),
      estimatedCompletion: estimatedCompletion,
    );
  }

  /// Bulk translation pro všechny jazyky
  Future<void> generateAllTranslations(String documentType) async {
    debugPrint('🌍 Generuji překlady pro všechny jazyky: $documentType');

    final sourceLanguage = 'cs'; // Czech as source

    for (final language in _supportedLanguages) {
      if (language.code == sourceLanguage) continue;

      final status = getTranslationStatus(documentType, language.code);
      if (status == TranslationStatus.notStarted) {
        await generateAutoTranslation(
          documentType: documentType,
          sourceLanguage: sourceLanguage,
          targetLanguage: language.code,
        );

        // Delay between translations
        await Future.delayed(const Duration(seconds: 1));
      }
    }

    _eventController.add(
      TranslationEvent(
        type: TranslationEventType.bulkTranslationCompleted,
        documentType: documentType,
        timestamp: DateTime.now(),
        data: {'languagesProcessed': _supportedLanguages.length - 1},
      ),
    );
  }

  /// Validace právní konzistence napříč jazyky
  Future<ConsistencyReport> validateLegalConsistency() async {
    debugPrint('⚖️ Validuji právní konzistenci napříč jazyky...');

    final inconsistencies = <LegalInconsistency>[];
    final documentTypes = _getAllDocumentTypes();

    for (final documentType in documentTypes) {
      final sourceDoc = _documents['cs']?[documentType]; // Czech as reference
      if (sourceDoc == null) continue;

      for (final language in _supportedLanguages) {
        if (language.code == 'cs') continue;

        final translatedDoc = _documents[language.code]?[documentType];
        if (translatedDoc == null) continue;

        // Kontrola verzí
        if (sourceDoc.version != translatedDoc.version) {
          inconsistencies.add(
            LegalInconsistency(
              documentType: documentType,
              language: language.code,
              type: InconsistencyType.versionMismatch,
              description:
                  'Version mismatch: source ${sourceDoc.version} vs translated ${translatedDoc.version}',
              severity: InconsistencySeverity.high,
            ),
          );
        }

        // Kontrola effective date
        if (sourceDoc.effectiveDate != translatedDoc.effectiveDate) {
          inconsistencies.add(
            LegalInconsistency(
              documentType: documentType,
              language: language.code,
              type: InconsistencyType.dateMismatch,
              description: 'Effective date mismatch',
              severity: InconsistencySeverity.medium,
            ),
          );
        }

        // Kontrola klíčových právních termínů
        final missingTerms = _checkLegalTerms(
          sourceDoc.content,
          translatedDoc.content,
        );
        if (missingTerms.isNotEmpty) {
          inconsistencies.add(
            LegalInconsistency(
              documentType: documentType,
              language: language.code,
              type: InconsistencyType.missingTerms,
              description: 'Missing legal terms: ${missingTerms.join(", ")}',
              severity: InconsistencySeverity.critical,
            ),
          );
        }
      }
    }

    return ConsistencyReport(
      checkedAt: DateTime.now(),
      totalDocuments: documentTypes.length * _supportedLanguages.length,
      inconsistencies: inconsistencies,
      overallScore: _calculateConsistencyScore(inconsistencies),
      recommendations: _generateConsistencyRecommendations(inconsistencies),
    );
  }

  /// Privátní pomocné metody
  Future<void> _ensureDocumentExists(
    String documentType,
    String languageCode,
  ) async {
    if (!isDocumentAvailable(documentType, languageCode)) {
      // Pokud dokument neexistuje, zkus automatický překlad
      if (languageCode != 'cs') {
        await generateAutoTranslation(
          documentType: documentType,
          sourceLanguage: 'cs',
          targetLanguage: languageCode,
        );
      } else {
        // Vytvoř základní český dokument
        await _createBaseDocument(documentType, languageCode);
      }
    }
  }

  Future<String> _translateTitle(String title, String targetLanguage) async {
    // Simulace překladu titulu
    final translations = {
      'en': {
        'Zásady ochrany osobních údajů': 'Privacy Policy',
        'Obchodní podmínky': 'Terms of Service',
        'Zásady cookies': 'Cookie Policy',
        'Smlouva o zpracování údajů': 'Data Processing Agreement',
      },
      'de': {
        'Zásady ochrany osobních údajů': 'Datenschutzrichtlinie',
        'Obchodní podmínky': 'Allgemeine Geschäftsbedingungen',
        'Zásady cookies': 'Cookie-Richtlinie',
        'Smlouva o zpracování údajů': 'Datenverarbeitungsvertrag',
      },
      'hr': {
        'Zásady ochrany osobních údajů': 'Pravila privatnosti',
        'Obchodní podmínky': 'Uvjeti korištenja',
        'Zásady cookies': 'Pravila kolačića',
        'Smlouva o zpracování údajů': 'Ugovor o obradi podataka',
      },
    };

    return translations[targetLanguage]?[title] ?? title;
  }

  Future<String> _simulateAITranslation(
    String content,
    String sourceLanguage,
    String targetLanguage,
  ) async {
    // Simulace AI překladu - v produkci by se použilo skutečné AI API
    await Future.delayed(const Duration(seconds: 2));

    return '''
[AI TRANSLATED FROM $sourceLanguage TO $targetLanguage]

$content

[This is a simulated AI translation. In production, this would be replaced with actual AI translation service like Google Translate API, DeepL API, or Azure Translator.]

Note: This translation requires human review for legal accuracy.
    ''';
  }

  List<String> _getAllDocumentTypes() {
    return [
      'privacy_policy',
      'terms_of_service',
      'cookie_policy',
      'data_processing_agreement',
    ];
  }

  Map<String, double> _calculateLanguageProgress() {
    final progress = <String, double>{};
    final documentTypes = _getAllDocumentTypes();

    for (final language in _supportedLanguages) {
      var completed = 0;
      for (final documentType in documentTypes) {
        final status = getTranslationStatus(documentType, language.code);
        if (status == TranslationStatus.approved) {
          completed++;
        }
      }
      progress[language.code] = (completed / documentTypes.length * 100);
    }

    return progress;
  }

  Map<String, double> _calculateDocumentProgress() {
    final progress = <String, double>{};

    for (final documentType in _getAllDocumentTypes()) {
      var completed = 0;
      for (final language in _supportedLanguages) {
        final status = getTranslationStatus(documentType, language.code);
        if (status == TranslationStatus.approved) {
          completed++;
        }
      }
      progress[documentType] = (completed / _supportedLanguages.length * 100);
    }

    return progress;
  }

  Future<DateTime> _estimateCompletionDate() async {
    final progress = await getTranslationProgress();
    final remainingTranslations =
        progress.totalTranslations - progress.completedTranslations;

    // Odhad: 1 překlad za den
    return DateTime.now().add(Duration(days: remainingTranslations));
  }

  List<String> _checkLegalTerms(
    String sourceContent,
    String translatedContent,
  ) {
    // Kontrola klíčových právních termínů
    final legalTerms = [
      'GDPR',
      'osobní údaje',
      'zpracování',
      'souhlas',
      'práva subjektu údajů',
    ];

    final missingTerms = <String>[];

    for (final term in legalTerms) {
      if (sourceContent.contains(term) &&
          !translatedContent.toLowerCase().contains(term.toLowerCase())) {
        missingTerms.add(term);
      }
    }

    return missingTerms;
  }

  double _calculateConsistencyScore(List<LegalInconsistency> inconsistencies) {
    if (inconsistencies.isEmpty) return 100.0;

    var score = 100.0;
    for (final inconsistency in inconsistencies) {
      switch (inconsistency.severity) {
        case InconsistencySeverity.critical:
          score -= 20;
          break;
        case InconsistencySeverity.high:
          score -= 10;
          break;
        case InconsistencySeverity.medium:
          score -= 5;
          break;
        case InconsistencySeverity.low:
          score -= 2;
          break;
      }
    }

    return score.clamp(0.0, 100.0);
  }

  List<String> _generateConsistencyRecommendations(
    List<LegalInconsistency> inconsistencies,
  ) {
    final recommendations = <String>[];

    if (inconsistencies.any(
      (i) => i.type == InconsistencyType.versionMismatch,
    )) {
      recommendations.add('Synchronizujte verze dokumentů napříč všemi jazyky');
    }

    if (inconsistencies.any((i) => i.type == InconsistencyType.missingTerms)) {
      recommendations.add('Proveďte review právních termínů v překladech');
    }

    if (inconsistencies.any(
      (i) => i.severity == InconsistencySeverity.critical,
    )) {
      recommendations.add('Okamžitě opravte kritické nekonzistence');
    }

    recommendations.addAll([
      'Implementujte automated consistency checking',
      'Proveďte legal review všech překladů',
      'Vytvořte glossary právních termínů pro překladatele',
    ]);

    return recommendations;
  }

  Future<void> _createBaseDocument(
    String documentType,
    String languageCode,
  ) async {
    // Vytvoření základního dokumentu v češtině
    final baseContent = _getBaseDocumentContent(documentType);

    final document = LegalDocument(
      id: '${documentType}_$languageCode',
      type: documentType,
      title: _getDocumentTitle(documentType),
      content: baseContent,
      language: languageCode,
      version: '1.0.0',
      effectiveDate: DateTime.now(),
      lastUpdated: DateTime.now(),
      reviewStatus: ReviewStatus.approved,
      metadata: {'isBaseDocument': true},
    );

    _documents[languageCode] ??= {};
    _documents[languageCode]![documentType] = document;

    final statusKey = '${documentType}_$languageCode';
    _translationStatus[statusKey] = TranslationStatus.approved;
  }

  String _getDocumentTitle(String documentType) {
    switch (documentType) {
      case 'privacy_policy':
        return 'Zásady ochrany osobních údajů';
      case 'terms_of_service':
        return 'Obchodní podmínky';
      case 'cookie_policy':
        return 'Zásady cookies';
      case 'data_processing_agreement':
        return 'Smlouva o zpracování údajů';
      default:
        return 'Právní dokument';
    }
  }

  String _getBaseDocumentContent(String documentType) {
    switch (documentType) {
      case 'privacy_policy':
        return '''
# Zásady ochrany osobních údajů

## 1. Úvod
Croatia Travel App respektuje vaše soukromí a zavazuje se chránit vaše osobní údaje.

## 2. Jaká data sbíráme
- Údaje o účtu (jméno, e-mail)
- Obsah deníku (texty, fotografie)
- Analytická data (statistiky používání)

## 3. Jak data používáme
Data používáme pro poskytování a zlepšování našich služeb.

## 4. Vaše práva
Máte právo na přístup, opravu, výmaz a přenositelnost svých dat.
        ''';
      case 'terms_of_service':
        return '''
# Obchodní podmínky

## 1. Popis služby
Croatia Travel App je digitální cestovní deník.

## 2. Povinnosti uživatele
Zavazujete se používat službu v souladu s těmito podmínkami.

## 3. Platební podmínky
Premium služby jsou zpoplatněny podle aktuálního ceníku.
        ''';
      default:
        return 'Základní obsah právního dokumentu.';
    }
  }

  /// Načítání a ukládání dat
  Future<void> _loadSupportedLanguages() async {
    await _createDefaultLanguages();
  }

  Future<void> _loadLegalDocuments() async {
    // Load documents from storage
  }

  Future<void> _initializeTranslationTracking() async {
    // Initialize translation status tracking
  }

  Future<void> _saveLegalDocuments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final documentsJson = <String, dynamic>{};

      _documents.forEach((language, docs) {
        documentsJson[language] = docs.map(
          (type, doc) => MapEntry(type, doc.toJson()),
        );
      });

      await prefs.setString(
        'multilingual_documents',
        jsonEncode(documentsJson),
      );
    } catch (e) {
      debugPrint('❌ Chyba při ukládání multilingual documents: $e');
    }
  }

  Future<void> _createDefaultLanguages() async {
    _supportedLanguages.addAll([
      SupportedLanguage(
        code: 'cs',
        name: 'Čeština',
        nativeName: 'Čeština',
        isRTL: false,
        isSupported: true,
        completionRate: 100.0,
      ),
      SupportedLanguage(
        code: 'en',
        name: 'English',
        nativeName: 'English',
        isRTL: false,
        isSupported: true,
        completionRate: 0.0,
      ),
      SupportedLanguage(
        code: 'de',
        name: 'German',
        nativeName: 'Deutsch',
        isRTL: false,
        isSupported: true,
        completionRate: 0.0,
      ),
      SupportedLanguage(
        code: 'hr',
        name: 'Croatian',
        nativeName: 'Hrvatski',
        isRTL: false,
        isSupported: true,
        completionRate: 0.0,
      ),
      SupportedLanguage(
        code: 'sk',
        name: 'Slovak',
        nativeName: 'Slovenčina',
        isRTL: false,
        isSupported: true,
        completionRate: 0.0,
      ),
    ]);

    // Vytvoření základních českých dokumentů
    for (final documentType in _getAllDocumentTypes()) {
      await _createBaseDocument(documentType, 'cs');
    }
  }

  void dispose() {
    _eventController.close();
  }

  // Gettery
  bool get isInitialized => _isInitialized;
  Map<String, Map<String, LegalDocument>> get documents =>
      Map.unmodifiable(_documents);
  List<SupportedLanguage> get supportedLanguages =>
      List.unmodifiable(_supportedLanguages);
}
