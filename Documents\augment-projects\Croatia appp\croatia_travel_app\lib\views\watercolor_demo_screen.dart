import 'package:flutter/material.dart';
import '../theme/watercolor_theme.dart';
import '../widgets/watercolor_background.dart';
import '../widgets/watercolor_icon.dart';

/// Demo screen pro ukázku watercolor designu
class WatercolorDemoScreen extends StatelessWidget {
  const WatercolorDemoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return WatercolorBackground(
      colors: WatercolorTheme.paperGradient,
      animated: true,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: Text(
            'Watercolor Design Demo',
            style: WatercolorTheme.headingMedium.copyWith(color: Colors.white),
          ),
          backgroundColor: Colors.transparent,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: WatercolorTheme.createWatercolorGradient(
                WatercolorTheme.adriaticGradient,
              ),
            ),
          ),
          leading: IconButton(
            onPressed: () => Navigator.pop(context),
            icon: WatercolorIcon(
              icon: Icons.arrow_back,
              size: 24,
              color: Colors.white,
              semanticLabel: 'Z<PERSON><PERSON><PERSON>',
            ),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(WatercolorTheme.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Nadpis
              Text(
                'Watercolor Design System',
                style: WatercolorTheme.headingLarge,
              ),
              const SizedBox(height: WatercolorTheme.spacingM),
              
              Text(
                'Krásný watercolor styl inspirovaný vaším designem',
                style: WatercolorTheme.bodyLarge,
              ),
              const SizedBox(height: WatercolorTheme.spacingL),

              // Watercolor ikony
              Text(
                'Watercolor Ikony',
                style: WatercolorTheme.headingMedium,
              ),
              const SizedBox(height: WatercolorTheme.spacingM),
              
              Wrap(
                spacing: WatercolorTheme.spacingM,
                runSpacing: WatercolorTheme.spacingM,
                children: [
                  _buildIconDemo(Icons.book, 'Deník', WatercolorTheme.adriaticBlue),
                  _buildIconDemo(Icons.place, 'Místa', WatercolorTheme.mediterraneanTeal),
                  _buildIconDemo(Icons.event, 'Události', WatercolorTheme.sunsetOrange),
                  _buildIconDemo(Icons.restaurant, 'Restaurace', WatercolorTheme.adriaticBlue),
                  _buildIconDemo(Icons.beach_access, 'Pláže', WatercolorTheme.mediterraneanTeal),
                  _buildIconDemo(Icons.directions_car, 'Doprava', WatercolorTheme.sunsetOrange),
                ],
              ),
              const SizedBox(height: WatercolorTheme.spacingL),

              // Watercolor karty
              Text(
                'Watercolor Karty',
                style: WatercolorTheme.headingMedium,
              ),
              const SizedBox(height: WatercolorTheme.spacingM),
              
              WatercolorCard(
                colors: [Colors.white, WatercolorTheme.creamPaper],
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        WatercolorIcon(
                          icon: Icons.location_city,
                          size: 32,
                          color: WatercolorTheme.adriaticBlue,
                          hasBackground: true,
                          backgroundColor: WatercolorTheme.adriaticBlue.withValues(alpha: 0.1),
                          semanticLabel: 'Město',
                        ),
                        const SizedBox(width: WatercolorTheme.spacingM),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Dubrovník',
                                style: WatercolorTheme.headingSmall,
                              ),
                              Text(
                                'Perla Jadranu',
                                style: WatercolorTheme.bodyMedium.copyWith(
                                  color: WatercolorTheme.charcoal.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: WatercolorTheme.spacingM),
                    Text(
                      'Dubrovník je nádherné historické město na chorvatském pobřeží, známé svými zachovalými středověkými hradbami a krásnou architekturou.',
                      style: WatercolorTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: WatercolorTheme.spacingM),

              WatercolorCard(
                colors: WatercolorTheme.mediterraneanGradient,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        WatercolorIcon(
                          icon: Icons.wb_sunny,
                          size: 32,
                          color: WatercolorTheme.sunsetOrange,
                          hasBackground: true,
                          backgroundColor: Colors.white.withValues(alpha: 0.3),
                          semanticLabel: 'Počasí',
                        ),
                        const SizedBox(width: WatercolorTheme.spacingM),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Krásné počasí',
                                style: WatercolorTheme.headingSmall.copyWith(color: Colors.white),
                              ),
                              Text(
                                '24°C, slunečno',
                                style: WatercolorTheme.bodyMedium.copyWith(
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: WatercolorTheme.spacingL),

              // Watercolor tlačítka
              Text(
                'Watercolor Tlačítka',
                style: WatercolorTheme.headingMedium,
              ),
              const SizedBox(height: WatercolorTheme.spacingM),
              
              Wrap(
                spacing: WatercolorTheme.spacingM,
                runSpacing: WatercolorTheme.spacingM,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {},
                    style: WatercolorTheme.watercolorButtonStyle(
                      backgroundColor: WatercolorTheme.adriaticBlue,
                    ),
                    icon: WatercolorIcon(
                      icon: Icons.favorite,
                      size: 20,
                      color: Colors.white,
                      semanticLabel: 'Oblíbené',
                    ),
                    label: const Text('Přidat do oblíbených'),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {},
                    style: WatercolorTheme.watercolorButtonStyle(
                      backgroundColor: WatercolorTheme.mediterraneanTeal,
                    ),
                    icon: WatercolorIcon(
                      icon: Icons.share,
                      size: 20,
                      color: Colors.white,
                      semanticLabel: 'Sdílet',
                    ),
                    label: const Text('Sdílet'),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {},
                    style: WatercolorTheme.watercolorButtonStyle(
                      backgroundColor: WatercolorTheme.sunsetOrange,
                    ),
                    icon: WatercolorIcon(
                      icon: Icons.navigation,
                      size: 20,
                      color: Colors.white,
                      semanticLabel: 'Navigace',
                    ),
                    label: const Text('Navigovat'),
                  ),
                ],
              ),
              const SizedBox(height: WatercolorTheme.spacingL),

              // Ručně psané poznámky
              Text(
                'Ručně psané poznámky',
                style: WatercolorTheme.headingMedium,
              ),
              const SizedBox(height: WatercolorTheme.spacingM),
              
              WatercolorCard(
                colors: [WatercolorTheme.creamPaper, Colors.white],
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Moje cestovní poznámky',
                      style: WatercolorTheme.handwrittenTitle,
                    ),
                    const SizedBox(height: WatercolorTheme.spacingS),
                    Text(
                      'Dnes jsem navštívil nádherný Dubrovník! Staré město je úžasné a výhled z hradeb je nezapomenutelný. Určitě se sem vrátím...',
                      style: WatercolorTheme.handwrittenNote,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: WatercolorTheme.spacingXL),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIconDemo(IconData icon, String label, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        WatercolorIcon(
          icon: icon,
          size: 32,
          color: color,
          hasBackground: true,
          backgroundColor: color.withValues(alpha: 0.1),
          semanticLabel: label,
        ),
        const SizedBox(height: WatercolorTheme.spacingS),
        Text(
          label,
          style: WatercolorTheme.labelMedium,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
